import { createApp } from 'vue';
import ViewUIPlus from 'view-ui-plus';
import ConfirmModal from './confirm-modal';
/**
 * 确认弹窗挂载
 * @author: hongshq
 * @date: 2023-04-18
 * */
const defaultData = {
  show: true,
  title: '',
  content: '',
  comments: '',
};

export default {
  install(app) {
    const ConfirmConstructor = createApp(ConfirmModal);
    ConfirmConstructor.use(ViewUIPlus);
    const instance = ConfirmConstructor.mount(document.createElement('div'));
    document.body.appendChild(instance.$el);
    app.config.globalProperties.$confirm = (options) => {
      Object.assign(instance, defaultData, options);
    };
  },
};
