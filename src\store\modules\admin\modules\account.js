/**
 * 注册、登录、注销
 * */
import { AccountLogin, AccountToken } from '@/api/account';
import { Modal } from 'view-ui-plus';
import biyiAuthUtil from '@/biyi-auth/init';
import util from '@/libs/util';
import router from '@/router';
import Setting from '@/config/setting';

export default {
  namespaced: true,
  actions: {
    /**
     * @description: 检查用户的登录状态
     * @param {类型} 参数名 描述
     * @return {类型} 描述
     */
    checkLoginStatus({ commit, dispatch }, to) {
      /**
       * @description: 截取ticket参数
       * @return {String} ticket Cas返回的凭证
       */
      function handleTicketParam() {
        const searchs = location.search.slice(1).split('&');
        for (let i = 0; i < searchs.length; i++) {
          if (searchs[i].includes('ticket=')) {
            return searchs[i].slice(7);
          }
        }
        return null;
      }
      // 自定义登录方法，根据实际情况补充
      function loginByCustom() {
        // eg：
        // router.push({
        //   name: 'customeLogin',
        //   query: {
        //     redirect: to.fullPath,
        //   },
        // })
      }
      // auth登录方法
      function loginByAuth() {
        return new Promise((resolve, reject) => {
          biyiAuthUtil
            .accountLogin()
            .then((res) => {
              if (res.token && res.token.token) {
                util.cookies.set('token', res.token.token);
                resolve();
              } else {
                router.push({
                  name: 'login',
                  query: {
                    redirect: to.fullPath,
                  },
                });
                reject(new Error('未检测到登录信息'));
              }
            })
            .catch((e) => {
              reject(e);
            });
        });
      }
      return new Promise(async (resolve, reject) => {
        try {
          // 检查用户登录信息
          const token = util.cookies.get('token');
          if (location.href.includes('ticket=')) {
            // cas回跳情况, 取ticket请求新的token
            const params = {
              ticket: handleTicketParam(),
              serviceUrl: util.cas.removeTicket(),
            };
            await dispatch('getTokenByTicket', params);
            resolve();
          } else if (token && token !== undefined) {
            // 登录有效
            resolve();
          } else if (Setting.isCasLogin) {
            // 无登录信息，判断登录类型，cas or biyi-auth
            util.cas.login();
          } else if (Setting.isCustomeLogin) {
            // 自定义登录
            loginByCustom();
          } else {
            await loginByAuth();
            resolve();
          }
        } catch (error) {
          reject(error);
        }
      });
    },

    /**
     * @description: 获取token
     * @param {String} ticket 获取token的凭证
     */
    getTokenByTicket({ dispatch }, params) {
      return new Promise((resolve, reject) => {
        // ajax请求
        // 保存token
        AccountToken(params)
          .then((res) => {
            util.cookies.set('token', res.token);
            resolve();
          })
          .catch((error) => {
            reject(error);
          });
      });
    },

    handleUnauthorized({ commit, dispatch }) {
      // 可扩展
      commit('admin/system/setUnauthorizedConfirm', true, { root: true });
    },

    handleReauthorization({ commit, dispatch }) {
      // 删除token
      util.cookies.remove('token');
      // util.cookies.remove('user-api-token');
      if (Setting.isCasLogin) {
        util.cas.login();
      } else {
        commit('admin/system/setUnauthorizedConfirm', false, { root: true });
        router.push({
          name: 'login',
        });
      }
    },
    /**
     * @description 登录
     * @param {Object} param context
     * @param {Object} param username {String} 用户账号
     * @param {Object} param password {String} 密码
     * @param {Object} param route {Object} 登录成功后定向的路由对象 任何 vue-router 支持的格式
     */
    login({ dispatch }) {
      return new Promise(async (resolve, reject) => {
        try {
          // 仅获取当前子应用的菜单
          const systemCode = Setting.qiankunRouterBase.substr(1);
          const info = await biyiAuthUtil.accountRefresh(systemCode);
          const accounrInfo = {
            menus: info[0].menus,
            tree: info[0].tree,
            access: info[1].permissionCode,
            ...info[1],
          };
          await dispatch('admin/user/set', accounrInfo, { root: true });
          // 用户登录后从持久化数据加载一系列的设置(目前主要是tags的加载)
          await dispatch('load');
          resolve();
        } catch (error) {
          reject(error);
        }
      });
    },
    /**
     * @description 自定义登录方法
     * @param {Object} param username {String} 用户账号
     * @param {Object} param password {String} 密码
     * @param {Object} param route {Object} 登录成功后定向的路由对象 任何 vue-router 支持的格式
     */
    customeLogin({ dispatch }, loginForm) {
      return new Promise(async (resolve, reject) => {
        try {
          const res = await AccountLogin(loginForm);
          util.cookies.set('token', res.token.token);
          router.replace('/');
          resolve();
        } catch (error) {
          reject(error);
        }
      });
    },
    /**
     * @description 退出登录
     * */
    logout({ commit, dispatch }, { confirm = false, vm } = {}) {
      async function logout() {
        // 删除cookie
        util.cookies.remove('token');
        // 清空 vuex 用户信息
        await dispatch('admin/user/set', {}, { root: true });
        // 清空系统设置
        commit('admin/system/setInitialized', false, { root: true });
        // 跳转路由
        if (Setting.isCasLogin) {
          util.cas.logout();
        } else {
          router.push({
            name: 'login',
          });
        }
      }

      if (confirm) {
        Modal.confirm({
          title: vm.$t('basicLayout.logout.confirmTitle'),
          content: vm.$t('basicLayout.logout.confirmContent'),
          onOk() {
            logout();
          },
        });
      } else {
        logout();
      }
    },
    /**
     * @description 用户登录后从持久化数据加载一系列的设置
     * @param {Object} state vuex state
     * @param {Object} dispatch vuex dispatch
     * @param {Object} loadOpenedTabs 是否加载页签信息
     */
    load({ state, dispatch }, { loadOpenedTabs = true } = {}) {
      return new Promise(async (resolve) => {
        // 加载用户登录信息
        // await dispatch('admin/user/load', null, { root: true });
        // 持久化数据加载上次退出时的多页列表
        await dispatch('admin/page/openedLoad', { loadOpenedTabs }, { root: true });
        // end
        resolve();
      });
    },
  },
};
