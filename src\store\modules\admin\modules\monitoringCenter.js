/**
 * “智能监控中心”页面数据
 * */
export default {
  namespaced: true,
  state: {
    // 车辆运行图
    vehicleMonitor: {
      refreshIndex: 1,
      refreshTime: '-',
      areaCode: '',
    },
    // 电池运行图
    batteryMonitor: {
      refreshIndex: 1,
      refreshTime: '-',
    },
    // 单车监控
    singleMonitor: {
      refreshIndex: 1,
      refreshTime: '-',
      skipRefresh: false,
      vin: '',
      isShowVehicleTrajectoryModal: false,
    },
  },
  mutations: {
    // ------ 车辆运行图 ------
    triggerVehicleMonitorRefresh(state) {
      if (state.vehicleMonitor.refreshIndex > 9) {
        state.vehicleMonitor.refreshIndex = 1;
      } else {
        state.vehicleMonitor.refreshIndex++;
      }
    },
    setVehicleMonitorRefreshTime(state, refreshTime) {
      state.vehicleMonitor.refreshTime = refreshTime;
    },
    setVehicleMonitorAreaCode(state, areaCode) {
      state.vehicleMonitor.areaCode = areaCode;
    },
    // ------ 电池运行图 ------
    triggerBatteryMonitorRefresh(state) {
      if (state.batteryMonitor.refreshIndex > 9) {
        state.batteryMonitor.refreshIndex = 1;
      } else {
        state.batteryMonitor.refreshIndex++;
      }
    },
    setBatteryMonitorRefreshTime(state, refreshTime) {
      state.batteryMonitor.refreshTime = refreshTime;
    },
    // ------ 单车监控 ------
    triggerSingleMonitorRefresh(state) {
      if (state.singleMonitor.skipRefresh) {
        return;
      }

      if (state.singleMonitor.refreshIndex > 9) {
        state.singleMonitor.refreshIndex = 1;
      } else {
        state.singleMonitor.refreshIndex++;
      }
    },
    setSingleMonitorRefreshTime(state, refreshTime) {
      state.singleMonitor.refreshTime = refreshTime;
    },
    setSingleMonitorSkipRefresh(state, skipRefresh) {
      state.singleMonitor.skipRefresh = skipRefresh;
    },
    setSingleMonitorVin(state, vin) {
      state.singleMonitor.vin = vin;
    },
    showVehicleTrajectoryModal(state) {
      state.singleMonitor.isShowVehicleTrajectoryModal = true;
      state.singleMonitor.skipRefresh = true;
    },
    closeVehicleTrajectoryModal(state) {
      state.singleMonitor.isShowVehicleTrajectoryModal = false;
      state.singleMonitor.skipRefresh = false;
    },
  },
};
