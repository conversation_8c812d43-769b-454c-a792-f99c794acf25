<template>
  <span class="i-layout-menu-side-title" :class="{ 'i-layout-menu-side-title-with-collapse': collapse }">
    <span v-if="withIcon" class="i-layout-menu-side-title-icon" :class="{ 'i-layout-menu-side-title-icon-single': hideTitle }">
      <Icon v-if="menu.icon" :type="menu.icon" />
      <Icon v-else-if="menu.custom" :custom="menu.custom" />
      <img v-else-if="menu.img" :src="menu.img" />
    </span>
    <span
      v-if="!hideTitle"
      class="i-layout-menu-side-title-text"
      :class="{
        'i-layout-menu-side-title-text-selected': selected,
        'i-layout-menu-side-title-text-with-subtitle': menu.subtitle,
        'i-layout-menu-side-title-text-with-icon': withIcon,
      }"
    >
      {{ tTitle(menu.title) }}
      <em v-if="menu.subtitle">{{ tTitle(menu.subtitle) }}</em>
    </span>
  </span>
</template>

<script>
import tTitle from '../mixins/translate-title';

export default {
  name: 'IMenuSideTitle',
  mixins: [tTitle],
  props: {
    menu: {
      type: Object,
      default() {
        return {};
      },
    },
    hideTitle: {
      type: Boolean,
      default: false,
    },
    // 用于侧边栏收起 Dropdown 当前高亮
    selected: {
      type: Boolean,
      default: false,
    },
    // 侧边栏折叠状态
    collapse: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    withIcon() {
      return this.menu.icon || this.menu.custom || this.menu.img;
    },
  },
};
</script>
