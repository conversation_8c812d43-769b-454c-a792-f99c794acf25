.page-wrap {
  height: 100%;
  padding: 0px 24px 24px;
}

.tree-table {
  height: 100%;
  transition: all 0;
  display: flex;
  .left-tree {
    width: 400px;
    height: 100%;
    min-height: 200px;
    flex-shrink: 0;
    background: #fff;
    border-radius: 4px;
    .left-top {
      display: flex;
      padding: 14px;
      justify-content: space-between;
      border-bottom: 1px solid rgb(248, 248, 248);
    }
    .tree {
      height: calc(~'100% - 55px');
      overflow-y: auto;
      padding: 0 16px;
    }
    /*
    .custom-tree-node {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 14px;
      padding-right: 8px;
    }
    */
  }
  .right-table {
    width: 100%;
    min-width: 1000px;
    margin: 0 0 0 24px;
    background: #fff;
    border-radius: 4px;
    .right-table-title {
      margin-bottom: 16px;
      display: flex;
      justify-content: space-between;
      border-bottom: 1px solid rgb(248, 248, 248);
      .title {
        padding: 14px 0 16px 16px;
        font-size: 16px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 600;
      }
    }
    .right-table-wrap {
      overflow: auto;
      border-radius: 4px;
      width: 100%;
      /*
      .search {
        padding: 16px 12px 0;
        background-color: #fff;
        overflow-y: auto;
        &-form {
          .ivu-col {
            padding-right: 12px;
          }
          .searchbtn {
            margin-right: 12px;
            justify-content: flex-end;
            display: flex;
          }
          .expand-all {
            color: #2d8cf0;
            margin-top: 5px;
            cursor: pointer;
          }
        }
      }
      */
      .content {
        padding: 0px 16px 16px;
        &-btn {
          display: flex;
          margin-bottom: 16px;
        }
        &-msg {
          font-weight: 700;
          margin-bottom: 12px;
        }
        &-table {
          position: relative;
          max-height: calc(~'100vh - 320px');
          min-height: 95px;
          overflow-y: auto;
        }
        .margin-right-8 {
          margin-right: 8px;
        }

        .action-btn {
          color: #2d8cf0;
          cursor: pointer;
        }
        .action-line {
          width: 1px;
          height: 14px;
          display: inline-block;
          background-color: #eee;
          margin: 0 8px;
          vertical-align: middle;
        }
      }
      .page {
        padding: 4px 16px;
      }
    }
    // .ivu-select-dropdown ul {
    //   text-align: center;
    // }
    :deep(.ivu-table-header thead tr th) {
      padding: 12px 0;
    }
    :deep(.ivu-table-fixed-header thead tr th) {
      padding: 12px 0;
    }
    :deep(.ivu-table-cell-with-selection) {
      text-overflow: unset;
    }
  }
}
