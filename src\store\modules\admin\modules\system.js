/**
 * 系统配置
 * */
import { cloneDeep } from 'lodash';
import routes from '@/router/routes';
import Setting from '@/config/setting';
import { getHeaderName, getMenuSider, getSiderSubmenu } from '@/libs/system';

export default {
  namespaced: true,
  state: {
    // 系统是否已初始化
    initialized: false,
    // 微前端子应用加载状态
    appLoading: true,
    // 是否显示未授权操作确认框
    unauthorizedConfirm: false,
  },
  mutations: {
    /**
     * @description 设置子应用的加载状态
     * @param {Object} state vuex state
     * @param {Boolean} value true: 已初始化 | false: 未初始化
     */
    setInitialized(state, value) {
      state.initialized = value;
    },
    /**
     * @description 设置子应用的加载状态
     * @param {Object} state vuex state
     * @param {Boolean} loading true: 正在加载 | false: 加载结束
     */
    setAppLoading(state, loading) {
      state.appLoading = loading;
    },
    /**
     * @description:
     * @param {类型} 参数名 描述
     * @return {类型} 描述
     */
    setUnauthorizedConfirm(state, value) {
      state.unauthorizedConfirm = value;
    },
  },
  actions: {
    /**
     * @description 进行系统一系列初始化工作
     */
    initSystem({ state, commit, dispatch }) {
      return new Promise(async (resolve) => {
        // 处理路由 得到每一级的路由设置
        commit('admin/page/init', routes, { root: true });
        // 设置顶栏菜单
        const menuHeader = this.state.admin.user.info.menus.header;
        commit('admin/menu/setHeader', menuHeader, { root: true });
        commit('setInitialized', true);
        resolve();
      });
    },
    /**
     * @description 监听系统的路由变化
     */
    watchRoute({ commit, dispatch }, to) {
      return new Promise(async (resolve) => {
        let menuSider = this.state.admin.user.info.menus.sider;
        const allMenus = cloneDeep(this.state.admin.user.info.menus.header);
        const home = {
          path: '/home',
          name: 'home',
          title: '首页',
          icon: 'md-home',
        };
        allMenus.forEach((v) => {
          v.path += '1';
        });
        allMenus.unshift(home);
        menuSider = Setting.layout.headerMenu ? menuSider : allMenus;
        let path = to.matched.length ? to.matched[to.matched.length - 1].path : to.path;
        if (!Setting.dynamicSiderMenu) {
          let headerName = getHeaderName(path, menuSider);
          if (headerName === null) {
            path = to.path;
            headerName = getHeaderName(path, menuSider);
          }
          // 在 404 时，是没有 headerName 的
          if (headerName !== null) {
            commit('admin/menu/setHeaderName', headerName, { root: true });
            commit('admin/menu/setMenuSider', menuSider, { root: true });

            const filterMenuSider = getMenuSider(menuSider, headerName);
            commit('admin/menu/setSider', filterMenuSider, { root: true });
            commit('admin/menu/setActivePath', to.path, { root: true });
            commit('admin/menu/setActiveName', to.name, { root: true });
            const openNames = getSiderSubmenu(path, menuSider);
            commit('admin/menu/setOpenNames', openNames, { root: true });
          } else {
            commit('admin/menu/setActivePath', '', { root: true });
            commit('admin/menu/setActiveName', '', { root: true });
          }
        }
        resolve();
      });
    },
  },
};
