<template>
  <div class="i-layout-menu-head-title">
    <span v-if="(item.icon || item.custom || item.img) && !hideIcon" class="i-layout-menu-head-title-icon">
      <Icon v-if="item.icon" :type="item.icon" />
      <Icon v-else-if="item.custom" :custom="item.custom" />
      <img v-else-if="item.img" :src="item.img" />
    </span>
    <span class="i-layout-menu-head-title-text">{{ tTitle(item.title) }}</span>
  </div>
</template>

<script>
/**
 * 该组件除了 Menu，也被 Breadcrumb 使用过
 * */
import tTitle from '../mixins/translate-title';

export default {
  name: 'IMenuHeadTitle',
  mixins: [tTitle],
  props: {
    item: {
      type: Object,
      default() {
        return {};
      },
    },
    hideIcon: {
      type: Boolean,
      default: false,
    },
  },
};
</script>
