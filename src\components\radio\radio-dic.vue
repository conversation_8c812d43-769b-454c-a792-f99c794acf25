<template>
  <div>
    <RadioGroup v-model="value" @on-change="changedata">
      <Radio v-for="(item, index) in optionlist" :key="index" :label="item.value" :disabled="disabled">{{ item.label }}</Radio>
    </RadioGroup>
  </div>
</template>
<script>
import { getDicData } from '@/api/common';

export default {
  props: {
    modelValue: {
      type: [String, Number],
      default: '',
    },
    dicType: {
      type: [String, Number],
      default: '',
    },
    placeholder: {
      type: String,
      default: '',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    clearable: {
      type: Boolean,
      default: true,
    },
    multiple: {
      type: Boolean,
      default: false,
    },
    selectedFirst: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      value: '',
      optionlist: [],
    };
  },
  watch: {
    modelValue: {
      immediate: true,
      handler: function (val) {
        this.value = `${val}`;
      },
    },
  },
  created() {
    if (this.dicType) {
      this.getOptionList();
    }
  },
  methods: {
    getOptionList() {
      getDicData({ dicType: this.dicType }).then((response) => {
        this.optionlist = response;
        if (this.selectedFirst && this.optionlist[0] && this.value === '') {
          this.value = this.optionlist[0].value; // 默认选中第一项
          this.$emit('init-value', this.value);
        }
      });
    },
    changedata(val) {
      this.$emit('update:modelValue', val);
    },
  },
  emits: ['init-value', 'update:modelValue'],
};
</script>
