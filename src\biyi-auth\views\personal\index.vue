<template>
  <div class="personal-wrap">
    <Row :gutter="24">
      <Col span="6">
        <Card title="个人设置" :padding="0" dis-hover :bordered="false">
          <CellGroup @on-click="changeMenu">
            <Cell
              v-for="(item, index) in menuList"
              :key="index"
              :title="item.title"
              :label="item.label"
              :name="item.name"
              :selected="actived == item.name"
            />
          </CellGroup>
        </Card>
      </Col>
      <Col span="18">
        <Card v-if="actived === 'base'">
          <h2>基本设置</h2>
          <Form
            :model="formPerson"
            ref="formPerson"
            :rules="personValidate"
            label-colon
            :label-width="100"
            key="formPerson"
            class="my-form"
          >
            <Row>
              <Col span="12">
                <FormItem label="用户名" prop="account">
                  <Input
                    v-model="formPerson.account"
                    placeholder="请输入用户名"
                    disabled
                    v-width="400"
                  />
                </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span="12">
                <FormItem label="姓名" prop="realname">
                  <Input
                    placeholder="请输入姓名"
                    v-model="formPerson.realname"
                    maxlength="“50”"
                    disabled
                    v-width="400"
                  ></Input>
                </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span="12">
                <FormItem label="所属组织" prop="orgName">
                  <Input
                    v-model="formPerson.orgName"
                    placeholder="请选择组织"
                    disabled
                    v-width="400"
                  />
                </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span="12">
                <FormItem label="挂职" prop="majorOrgName">
                  <Input v-model="formPerson.majorOrgName" placeholder="" disabled v-width="400" />
                </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span="12">
                <FormItem label="邮箱" prop="email">
                  <Input
                    type="text"
                    v-model="formPerson.email"
                    placeholder="请输入邮箱"
                    v-width="400"
                  ></Input>
                </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span="12">
                <FormItem label="手机" prop="phone">
                  <Input
                    type="text"
                    v-model="formPerson.phone"
                    placeholder="请输入手机号"
                    v-width="400"
                  ></Input>
                </FormItem>
              </Col>
            </Row>
          </Form>
          <Button type="primary" @click="sureClick('base')">确定</Button>
          <!-- <Button type="primary" @click="sureClick('cancel')">取消</Button> -->
        </Card>
        <Card v-if="actived === 'password'">
          <h2>修改密码</h2>
          <Form
            :model="formPassword"
            ref="formPassword"
            :rules="emailValidate"
            label-colon
            :label-width="100"
            key="formPassword"
            class="my-form"
          >
            <Row>
              <Col span="12">
                <FormItem label="旧密码" prop="oldPPP">
                  <Input
                    type="password"
                    v-model="formPassword.oldPPP"
                    placeholder="请输入旧密码"
                    v-width="400"
                  />
                </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span="12">
                <FormItem label="输入新密码" prop="newPPP" style="position: relative">
                  <Input
                    type="password"
                    placeholder="请输入新密码"
                    v-model="formPassword.newPPP"
                    v-width="400"
                  ></Input>
                  <Tooltip style="position: absolute" max-width="600" transfer>
                    <Icon
                      type="ios-help-circle-outline"
                      size="26"
                      color="#dcdee2"
                      class="password-info"
                    />
                    <template #content>
                      <p>1. 密码长度应大于等于10，小于等于20个字符</p>
                      <p>2. 密码应包含大写字母、小写字母、数字、#@￥%&’*()+等特殊字符至少三项</p>
                      <p>3. 密码不应包含用户名信息，ctsi，ctdi</p>
                      <p>4. 密码不应包含键盘上横向或者纵向任意连续的四个字符或shift转换键</p>
                      <p>5. 密码不能是 密码字典表里已存在的密码</p>
                    </template>
                  </Tooltip>
                </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span="12">
                <FormItem label="确认新密码" prop="reNewPPP">
                  <Input
                    type="password"
                    v-model="formPassword.reNewPPP"
                    placeholder="请再次确认新密码"
                    v-width="400"
                  />
                </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span="12">
                <FormItem prop="code" label="邮箱验证码">
                  <Input
                    type="text"
                    v-model="formPassword.code"
                    placeholder="请输入邮箱验证码"
                    v-width="400"
                  >
                    <template #append>
                      <biyi-captcha
                        type="dragCaptcha"
                        ref="verificationCode"
                        :baseUrl="baseUrl"
                        v-model="showCaptcha"
                        @getVerifyRes="getVerifyRes"
                        class="btn"
                        :saveStatus="false"
                      />
                      <span v-if="!disabled" style="cursor: pointer" @click="checkInfo">
                        点击验证
                      </span>
                      <span v-else style="cursor: not-allowed">重新获取({{ count }})</span>
                    </template>
                  </Input>
                </FormItem>
              </Col>
            </Row>
          </Form>
          <Button type="primary" @click="sureClick('password')">确定</Button>
          <!-- <Button type="primary" @click="sureClick('cancel')">取消</Button> -->
        </Card>
        <!-- </Card> -->
      </Col>
    </Row>
  </div>
</template>
<script>
import biyiCaptcha from '@/biyi-captcha/src/index.js';
import { getPersonInfo, updatePersonInfo, resetPersonPassword } from '../../api/account';
import { regEmail, regPhone } from '../../libs/reg';
import { sendEmail, validateEmailAndCode } from '../../api/register';
import { validatePasswordFn } from '../../libs/validatePassword';
import { rsaContent } from '../../libs/rsa';

export default {
  name: 'personalPage',
  data() {
    const validatePhone = (rule, value, callback) => {
      const reg = regPhone;
      if (!reg.test(value)) {
        callback(new Error('请输入正常的手机格式'));
      }
      callback();
    };
    const validateEmail = (rule, value, callback) => {
      const reg = regEmail;
      if (!reg.test(value)) {
        callback(new Error('请输入正常的邮箱格式'));
      }
      callback();
    };
    const validatePassword = (rule, value, callback) => {
      const reg = validatePasswordFn(value);
      if (!reg || (value && value.includes(this.formPerson.account))) {
        callback(new Error('密码格式错误，请参考密码合规规范'));
      }
      if (value === this.formPassword.oldPPP) {
        callback(new Error('新密码不能与旧密码一致'));
      }
      callback();
    };
    const validateNewPassword = (rule, value, callback) => {
      if (value !== this.formPassword.newPPP) {
        callback(new Error('两次输入的密码不一致'));
      }
      callback();
    };
    return {
      formPerson: {
        account: '',
        realname: '',
        orgName: '',
        majorOrgName: '',
        email: '',
        phone: '',
      },
      count: 60,
      disabled: false,
      showCaptcha: false,
      formPassword: {
        oldPPP: '',
        newPPP: '',
        reNewPPP: '',
        code: '',
      },
      personValidate: {
        email: [
          { required: true, message: '请填写邮箱', trigger: 'blur' },
          { validator: validateEmail, trigger: 'blur' },
        ],
        phone: [
          { required: true, message: '请填写手机号', trigger: 'blur' },
          { validator: validatePhone, trigger: 'blur' },
        ],
      },
      emailValidate: {
        oldPPP: [{ required: true, message: '请输入旧密码', trigger: 'blur' }],
        newPPP: [
          { required: true, message: '请输入新密码', trigger: 'blur' },
          { validator: validatePassword, trigger: 'blur' },
        ],
        reNewPPP: [
          { required: true, message: '请再次确认新密码', trigger: 'blur' },
          { validator: validateNewPassword, trigger: 'blur' },
        ],
        code: [{ required: true, message: '请输入邮箱验证码', trigger: 'blur' }],
      },
      actived: 'base',
      menuList: [
        {
          title: '基本设置',
          name: 'base',
          label: '个人账户信息设置',
        },
        {
          name: 'password',
          title: '修改密码',
          label: '修改密码',
        },
      ],
      captchaInfo: null,
      // baseUrl: this.$biyiSetting.authAPIUrl || 'http://192.168.111.138:9004'
    };
  },
  computed: {
    baseUrl() {
      return window.$biyiAuthAPIUrl;
      // return this.$biyiSetting.authAPIUrl; // [MEMO VU3升级] 原版比翼框架是在安装的时候设置了 Vue.prototype.$biyiSetting 变量
    },
  },
  components: {
    biyiCaptcha,
  },
  methods: {
    sureClick(type) {
      // 保存基础信息
      if (type === 'base') {
        this.$refs.formPerson.validate((valid) => {
          if (valid) {
            // this.saveHttp()保存基础信息
            this.saveBaseInfo();
          }
        });
      }
      if (type === 'password') {
        this.$refs.formPassword.validate((valid) => {
          if (valid) {
            // this.saveHttp()修改密码保存
            this.validateCode();
          }
        });
      }
    },
    // 验证邮箱验证码
    validateCode() {
      validateEmailAndCode({
        code: this.formPassword.code,
        codeType: 'reset_personal',
        email: this.formPerson.email,
      }).then((res) => {
        console.log(res);
        this.resetPassword(res);
      });
    },
    checkInfo() {
      this.showCaptcha = true;
    },
    // 保存重置密码
    resetPassword(sessionId) {
      resetPersonPassword({
        oldPassword: rsaContent(this.formPassword.oldPPP),
        resetPassword: rsaContent(this.formPassword.newPPP),
        userName: this.formPerson.account,
        sessionId,
        type: 'reset_personal',
      }).then((res) => {
        if (res) {
          this.$Message.error({
            content: res,
            duration: 10,
            closable: true,
          });
          return;
        }
        this.$Message.info('更新成功');
        console.log(res);
      });
    },
    saveBaseInfo() {
      const { email, phone } = this.formPerson;
      updatePersonInfo({
        email,
        phone,
      }).then((res) => {
        this.$Message.info('更新成功');
        console.log(res);
      });
    },
    getVerifyRes(info) {
      this.showCaptcha = false;
      this.captchaInfo = info;
      this.startSendEmail();
    },
    setCountDown() {
      this.count = 60;
      this.disabled = true;
      let timer = setInterval(() => {
        if (this.count > 0) {
          this.count--;
        } else {
          this.disabled = false;
          clearInterval(timer);
        }
      }, 1000);
    },
    startSendEmail() {
      sendEmail({ userName: this.formPerson.account, type: 'reset_personal' }, this.captchaInfo)
        .then((res) => {
          if (res.code === '1000') {
            this.setCountDown();
            this.$Message.success(res.msg);
          } else {
            this.$Message.error(res.msg);
          }
        })
        .catch((err) => {
          console.log(err.response);
          // this.$Message.error(err.response.message)
        });
    },
    changeMenu(name) {
      // if (this.currentTab !== name) {
      this.actived = name;
      if (name === 'password') {
        this.formPassword = {
          oldPPP: '',
          newPPP: '',
          reNewPPP: '',
          code: '',
        };
      }
      // }
    },
    getBaseInfo() {
      getPersonInfo().then((res) => {
        this.formPerson = res;
      });
    },
  },
  mounted() {
    this.getBaseInfo();
  },
};
</script>
<style lang="less" scoped>
.personal-wrap {
  padding: 16px 24px;
  height: 100%;
  :deep(.ivu-card-head span) {
    font-size: 16px;
    font-weight: 600;
    color: #17233d;
    line-height: 22px;
  }
  :deep(.ivu-cell) {
    // height: 82px;
    // line-height: 82px;
    padding: 16px 0px 16px 16px;
  }
  :deep(.ivu-cell-title) {
    font-size: 16px;
    font-weight: 400;
    // color: #515a6e;
    line-height: 22px;
  }
  :deep(.ivu-cell-label) {
    font-size: 14px;
    font-weight: 400;
    // color: #808695;
    line-height: 20px;
    padding-top: 8px;
  }
  :deep(.ivu-cell-selected) {
    color: #2d8cf0;
  }
  :deep(.ivu-cell-selected:hover) {
    color: #2d8cf0;
  }
  .setting-content {
    padding: 24px 0;
  }
  :deep(.ivu-row) {
    height: 100%;
  }
  :deep(.ivu-card) {
    height: 100%;
    min-height: 500px;
  }
  :deep(.ivu-col-span-18) {
    height: 100%;
  }
  :deep(.ivu-divider-horizontal) {
    margin: 0 0;
  }
}
.btn {
  border: 0;
  outline: 0;
  width: 150px !important;
}
.my-form {
  padding: 24px 0;
}
.password-info {
  &:hover {
    color: #2d8cf0 !important;
  }
}
:deep(.ivu-tooltip-rel) {
  vertical-align: sub;
  cursor: pointer;
}
</style>
