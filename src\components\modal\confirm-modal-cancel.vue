<template>
  <Modal v-model="visible" class="default-modal" :width="width" :mask-closable="false" @on-ok="handleSubmit" @on-cancel="handleCancle">
    <template v-slot:header>
      <div>
        <div class="title-span" />
        <b>{{ title }}</b>
      </div>
    </template>
    <div class="content">
      <img src="@/assets/images/tip_warn_orange.png" style="width: 50px; margin-bottom: 6px" />
      <p>{{ content }}</p>
    </div>
    <div v-if="comments" class="comments">
      <p>{{ comments }}</p>
    </div>
    <template v-slot:footer>
      <Button @click="handleCancle">取消</Button>
      <Button type="primary" @click="handleSubmit">确定</Button>
    </template>
  </Modal>
</template>

<script>
/**
 * 确认弹窗
 * @author: hongshq
 * @date: 2023-04-18
 * **/
export default {
  data() {
    return {
      show: false,
      title: '',
      content: '',
      comments: '',
      width: 416,
      onOk: null,
      onCancel: null,
    };
  },
  computed: {
    visible: {
      get() {
        return this.show;
      },
      set() {},
    },
  },
  methods: {
    handleSubmit() {
      if (this.onOk) {
        this.onOk();
      }
      this.show = false;
    },
    handleCancle() {
      if (this.onCancel) {
        this.onCancel();
      }
      this.show = false;
    },
  },
};
</script>
<style lang="less" scoped>
.default-modal {
  .title-span {
    display: inline-block;
    height: 12px;
    width: 10px;
    border-left: solid 3px #308eff;
  }
  .ivu-modal {
    max-height: 80vh;
    overflow: auto;
  }
  .content {
    text-align: center;
  }
  .comments {
    padding-left: 20px;
    color: #c5c8ce;
  }
  .ivu-icon {
    color: rgb(245, 173, 23);
    margin-right: 4px;
  }
  :deep(.ivu-modal-footer) {
    text-align: right;
    padding: 12px 18px;
    .ivu-btn-text {
      border: 1px solid rgb(236, 236, 236);
    }
  }
  .ivu-modal-body {
    padding: 24px 16px;
  }
}
</style>
