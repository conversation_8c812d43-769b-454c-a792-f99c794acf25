/*
  使用示例
    mixins: [mixinsPageTree],
    data() {
      const isExpandAllTreeNode = false; // 打开页面的时候，全部收缩树的节点
      return {
        // 通用-树
        treeLoading: true,
        selectedTreeNode: {},
        treeData: [],
        isExpandAllTreeNode,
        hasCancelTreeSelect: false,
        // 页面配置-API
        pageConfigAPI: {
          getTree: {
            apiFun: getPoolUsedTree,
            replaceResponseData: ({ responseData }) => {
              const { treeData } = translateTreeData({
                data: responseData,
                nameAttr: 'groupName',
                expandAll: isExpandAllTreeNode,
                convertItemData: ({ item, orgItem }) => {
                  item.groupType = orgItem.groupType; // 根据 groupType，右侧列表显示的内容不同
                },
              });
              return treeData;
            },
          },
        },
      };
    },
    mounted() {
      this.initPage()
    },
    methods: {
      refreshRightContent() {
        // 单击树节点后会调用该方法，显示右侧内容。需要根据页面业务来重写
      },
    },

  说明：
    hasCancelTreeSelect    true: 第二次单击选择节点可以取消选中
*/
import { mapState } from 'vuex';

import { createSearchFormlist } from './createSearchForm.js';
import { createListColumns } from './createListColumns.js';

export default {
  computed: {
    ...mapState('admin/user', ['permissionCodeDict']),
    ...mapState('admin/pageData', ['pageDataDict']),
    currentPageData: function () {
      const activeName = this.$route.name;
      if (activeName && this.pageDataDict && this.pageDataDict[activeName]) {
        return this.pageDataDict[activeName];
      }
      return {};
    },
    /**
     * 根据页面设置的 pageConfigButton 和 store 里 存储的 permissionCodeDict 生成按钮权限
     */
    pagePermission: function () {
      let pagePermission = {};
      const pageConfigButton = this.pageConfigButton || {};
      Object.keys(pageConfigButton).forEach((key) => {
        const permCode = pageConfigButton[key].permCode;
        pagePermission[key] = permCode ? this.permissionCodeDict[permCode] : false;
      });
      return pagePermission;
    },

    /**
     * 根据 pageItemsConfig 的配置，生成列表的 columns
     */
    listColumns: function () {
      const listColumns = createListColumns({
        listColumnsOrder: this.listColumnsOrder,
        pageItemsConfig: this.pageItemsConfig,
      });

      // 序号列
      const iINDEX = this.listColumnsOrder.indexOf('INDEX');
      if (iINDEX !== -1) {
        listColumns[iINDEX].indexMethod = (row, index) => {
          return row._index + 1 + this.listConfig.size * (this.listConfig.page - 1);
        };
      }

      return listColumns;
    },
    /**
     * 根据 pageItemsConfig 的配置，生成搜索的 columns
     */
    searchFormItems: function () {
      return createSearchFormlist({
        pageItemsConfig: this.pageItemsConfig,
      });
    },
  },
  methods: {
    /**
     * 打开页面
     */
    async initPage() {
      const { treeData } = await this.getTreeData();
      this.restorePageData({ treeData });
      this.treeData = treeData;
    },
    /**
     * 获取树的数据
     */
    getTreeData() {
      const {
        apiFun,
        successMsg,
        errMsg,
        convertRequestData = () => {}, // 根据页面业务更改 requestData
        replaceRequestData,
        convertResponseData = () => {}, // 根据页面业务更改 responseData
        replaceResponseData,
      } = this.pageConfigAPI.getTree;
      return new Promise(async (resolve, reject) => {
        this.treeLoading = true;

        let requestData = {};
        convertRequestData({ requestData });
        if (replaceRequestData) {
          requestData = replaceRequestData({ requestData });
        }

        try {
          const response = await apiFun(requestData);

          let responseData = response || [];
          convertResponseData({ responseData });
          if (replaceResponseData) {
            responseData = replaceResponseData({ responseData });
          }

          resolve({
            treeData: responseData,
          });
          this.treeLoading = false;
        } catch (error) {
          resolve({
            treeData: [],
          });
          this.treeLoading = false;
        }
      });
    },
    /**
     * 全部展开/收起数的节点
     */
    expandAllTreeNode(isExpandAll) {
      this.isExpandAllTreeNode = isExpandAll;
      this.expandTreeNode(this.treeData, isExpandAll);
    },
    expandTreeNode(children, expand) {
      children.forEach((item) => {
        item.expand = expand;
        const children = item.children || [];
        this.expandTreeNode(children, expand);
      });
    },
    onTreeSelectChange(selectedAry, selectedNode) {
      if (this.selectedTreeNode.id !== selectedNode.id) {
        this.selectedTreeNode = selectedNode;
        this.$nextTick(() => {
          this.refreshRightContent();
        });
      }

      if (!this.hasCancelTreeSelect) {
        // 不可以通过第二次单击来取消 Tree 节点的选择状态
        selectedNode.selected = true;
      }
    },
    /**
     * 更新树右侧的内容，需要根据页面业务来重写
     */
    refreshRightContent() {
      if (this.$refs.rightList) {
        this.$refs.rightList.refreshPage();
      }
    },
    /**
     * 跳转到子页面
     */
    toRouter({ routerName, params = {}, savePageData = true }) {
      this.$router.push({
        name: routerName,
        params: {
          ...params,
          toChildrenPage: true,
          savePageData: savePageData ? this.savePageData() : null, // 从子页面跳转回来的时候，需要恢复本页面的搜索、分页的数据
        },
      });
    },
    savePageData() {
      let treeSelectedNodes = [];
      if (this.$refs.leftTree) {
        treeSelectedNodes = this.$refs.leftTree.getSelectedNodes();
      }

      let rightListData = {};
      if (this.$refs.rightList) {
        rightListData = this.$refs.rightList.savePageData();
      }

      return JSON.stringify({
        leftTreeData: {
          treeSelectedNodes,
        },
        rightListData,
      });
    },
    /**
     * 恢复页面数据（分页、搜索）。用于从详情页面返回列表页面的时候。
     */
    restorePageData({ treeData = [] }) {
      const currentPageData = this.currentPageData || {};
      const leftTreeData = currentPageData.leftTreeData || {};
      const treeSelectedNodes = leftTreeData.treeSelectedNodes || [];

      if (treeSelectedNodes.length === 0 || treeData.length === 0) {
        return;
      }

      this.selectedTreeNode = treeSelectedNodes[0]; // TODO: 只处理了单选的 Tree
      const treeSelectedNodeIds = treeSelectedNodes.map((item) => {
        return item.id;
      });
      this.restoreTreeNodeSelected(treeData, treeSelectedNodeIds);

      if (currentPageData.rightListData && this.$refs.rightList) {
        this.$refs.rightList.restoreListSearch(currentPageData.rightListData);
      }
    },
    restoreTreeNodeSelected(children, treeSelectedNodeIds = []) {
      let isSelectedChildren = false;
      children.forEach((item) => {
        if (treeSelectedNodeIds.includes(item.id)) {
          item.selected = true;
          isSelectedChildren = true;
        }
        const children = item.children || [];
        const res = this.restoreTreeNodeSelected(children, treeSelectedNodeIds);
        if (res.isSelectedChildren) {
          item.expand = true;
          isSelectedChildren = true;
        }
      });

      return { isSelectedChildren };
    },
  },
};
