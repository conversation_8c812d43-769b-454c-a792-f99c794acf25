export function sleep(value = 3) {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve();
    }, value * 1000);
  });
}

export function formatDateTime(val) {
  const currentDate = val ? new Date(val) : new Date();

  const year = currentDate.getFullYear();
  const month = (currentDate.getMonth() + 1).toString().padStart(2, 0);
  const day = currentDate.getDate().toString().padStart(2, 0);

  const h = currentDate.getHours().toString().padStart(2, 0);
  const m = currentDate.getMinutes().toString().padStart(2, 0);
  const s = currentDate.getSeconds().toString().padStart(2, 0);

  const weekStrAry = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
  const weekIndex = currentDate.getDay();
  const week = weekStrAry[weekIndex];

  return {
    year,
    month,
    day,
    h,
    m,
    s,
    week,
  };
}

// 获取近*天（默认获取近7天）
export function getLastDays(days = 7) {
  const endDate = new Date();
  const endY = endDate.getFullYear();
  const endM = (endDate.getMonth() + 1).toString().padStart(2, 0);
  const endD = endDate.getDate().toString().padStart(2, 0);

  const beginDate = new Date(endDate.valueOf() - 86400000 * days);
  const beginY = beginDate.getFullYear();
  const beginM = (beginDate.getMonth() + 1).toString().padStart(2, 0);
  const beginD = beginDate.getDate().toString().padStart(2, 0);

  return {
    beginDate: `${beginY}-${beginM}-${beginD}`,
    endDate: `${endY}-${endM}-${endD}`,
  };
}

// 获取近*天（默认获取近30天）
export function getLastAllDays(days = 30) {
  let baseDate = +new Date();

  let ary = [];
  for (let i = 0; i < days; i++) {
    const date = new Date((baseDate -= 1000 * 3600 * 24));
    const y = date.getFullYear();
    const m = (date.getMonth() + 1).toString().padStart(2, 0);
    const d = date.getDate().toString().padStart(2, 0);

    ary.push([y, m, d].join('-'));
  }
  ary.reverse();

  return ary;
}

// 获取近*分钟，间隔*分钟
export function getLastAllTime(range = 60 * 24, interval = 5) {
  let baseDate = +new Date();

  let ary = [];
  const num = range / 5;
  for (let i = 0; i < num; i++) {
    const date = new Date((baseDate -= 1000 * 60 * interval));
    const y = date.getFullYear();
    const m = (date.getMonth() + 1).toString().padStart(2, 0);
    const d = date.getDate().toString().padStart(2, 0);
    const h = date.getHours().toString().padStart(2, 0);
    const mm = date.getMinutes().toString().padStart(2, 0);
    const s = date.getSeconds().toString().padStart(2, 0);

    ary.push(`${[y, m, d].join('-')} ${[h, mm, s].join(':')}`);
  }

  ary.reverse();

  return ary;
}

// 获取当前日期对应的月份的第一天和最后一天
export function getCurrentMonth(val) {
  const date = val ? new Date(val) : new Date();

  const year = date.getFullYear();

  let month = (date.getMonth() + 1).toString();
  if (month.length === 1) {
    month = `0${month}`;
  }

  const startDay = '01';

  // 获取当前月的最后一天。参数0代表上个月的最后一天
  const endDay = new Date(year, month, 0).getDate().toString();

  return {
    beginDate: `${year}-${month}-${startDay}`,
    endDate: `${year}-${month}-${endDay}`,
  };
}

// 获取当前日期的上个月的第一天和最后一天
export function getPreMonth(val) {
  const date = val ? new Date(val) : new Date();

  let year = date.getFullYear();

  let month = date.getMonth();
  if (month === 0) {
    month = 12;
    year = year - 1;
  }
  month = month.toString();
  if (month.length === 1) {
    month = `0${month}`;
  }

  const startDay = '01';

  // 获取当前月的最后一天。参数0代表上个月的最后一天
  const endDay = new Date(year, month, 0).getDate().toString();

  return {
    year,
    month,
    beginDate: `${year}-${month}-${startDay}`,
    endDate: `${year}-${month}-${endDay}`,
  };
}

// 获取当前日期的下个月的第一天和最后一天
export function getNextMonth(val) {
  const date = val ? new Date(val) : new Date();

  let year = date.getFullYear();

  let month = date.getMonth() + 2;
  if (month > 12) {
    month = month - 12;
    year = year + 1;
  }
  month = month.toString();
  if (month.length === 1) {
    month = `0${month}`;
  }

  const startDay = '01';

  // 获取当前月的最后一天。参数0代表上个月的最后一天
  const endDay = new Date(year, month, 0).getDate().toString();

  return {
    beginDate: `${year}-${month}-${startDay}`,
    endDate: `${year}-${month}-${endDay}`,
  };
}

// 获取近*小时
export function getLastHours(hours = 1) {
  const endDate = new Date();
  const endY = endDate.getFullYear();
  const endM = (endDate.getMonth() + 1).toString().padStart(2, 0);
  const endD = endDate.getDate().toString().padStart(2, 0);
  const endH = endDate.getHours().toString().padStart(2, 0);
  const endMM = endDate.getMinutes().toString().padStart(2, 0);
  const endS = endDate.getSeconds().toString().padStart(2, 0);

  const beginDate = new Date(endDate.valueOf() - 3600000 * hours);
  const beginY = beginDate.getFullYear();
  const beginM = (beginDate.getMonth() + 1).toString().padStart(2, 0);
  const beginD = beginDate.getDate().toString().padStart(2, 0);
  const beginH = beginDate.getHours().toString().padStart(2, 0);
  const beginMM = beginDate.getMinutes().toString().padStart(2, 0);
  const beginS = beginDate.getSeconds().toString().padStart(2, 0);

  return {
    beginDate: `${beginY}-${beginM}-${beginD} ${beginH}:${beginMM}:${beginS}`,
    endDate: `${endY}-${endM}-${endD} ${endH}:${endMM}:${endS}`,
  };
}

// 获取近*分钟
export function getLastMinutes(minutes = 1) {
  const endDate = new Date();
  const endY = endDate.getFullYear();
  const endM = (endDate.getMonth() + 1).toString().padStart(2, 0);
  const endD = endDate.getDate().toString().padStart(2, 0);
  const endH = endDate.getHours().toString().padStart(2, 0);
  const endMM = endDate.getMinutes().toString().padStart(2, 0);
  const endS = endDate.getSeconds().toString().padStart(2, 0);

  const beginDate = new Date(endDate.valueOf() - 60000 * minutes);
  const beginY = beginDate.getFullYear();
  const beginM = (beginDate.getMonth() + 1).toString().padStart(2, 0);
  const beginD = beginDate.getDate().toString().padStart(2, 0);
  const beginH = beginDate.getHours().toString().padStart(2, 0);
  const beginMM = beginDate.getMinutes().toString().padStart(2, 0);
  const beginS = beginDate.getSeconds().toString().padStart(2, 0);

  return {
    beginDate: `${beginY}-${beginM}-${beginD} ${beginH}:${beginMM}:${beginS}`,
    endDate: `${endY}-${endM}-${endD} ${endH}:${endMM}:${endS}`,
    beginY,
    beginM,
    beginD,
    beginH,
    beginMM,
    beginS,
    endY,
    endM,
    endD,
    endH,
    endMM,
    endS,
  };
}

export function getDateRange(startDate, minutes = 1) {
  const beginDate = startDate.getFullYear ? startDate : new Date(startDate);
  const beginY = beginDate.getFullYear();
  const beginM = (beginDate.getMonth() + 1).toString().padStart(2, 0);
  const beginD = beginDate.getDate().toString().padStart(2, 0);
  const beginH = beginDate.getHours().toString().padStart(2, 0);
  const beginMM = beginDate.getMinutes().toString().padStart(2, 0);
  const beginS = beginDate.getSeconds().toString().padStart(2, 0);

  const endDate = new Date(beginDate.valueOf() + 60000 * minutes);
  const endY = endDate.getFullYear();
  const endM = (endDate.getMonth() + 1).toString().padStart(2, 0);
  const endD = endDate.getDate().toString().padStart(2, 0);
  const endH = endDate.getHours().toString().padStart(2, 0);
  const endMM = endDate.getMinutes().toString().padStart(2, 0);
  const endS = endDate.getSeconds().toString().padStart(2, 0);

  return {
    beginDate: `${beginY}-${beginM}-${beginD} ${beginH}:${beginMM}:${beginS}`,
    endDate: `${endY}-${endM}-${endD} ${endH}:${endMM}:${endS}`,
    beginY,
    beginM,
    beginD,
    beginH,
    beginMM,
    beginS,
    endY,
    endM,
    endD,
    endH,
    endMM,
    endS,
  };
}

// DatePicker用
export function valueOfDatePicker(date, type) {
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, 0);
  const day = date.getDate().toString().padStart(2, 0);

  switch (type) {
    case 'date':
      return new Date(`${year}-${month}-${day}`).valueOf();
    case 'month':
      return new Date(`${year}-${month}-01`).valueOf();
    default:
      return date.valueOf();
  }
}
