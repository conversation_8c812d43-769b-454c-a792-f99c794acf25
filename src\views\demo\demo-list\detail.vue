<template>
  <div>
    <typical-page-header :header-title="pageTitle" />
    <div class="form">
      <LoadingDetail :isLoading="isLoadingGetDetail" />
      <Form v-if="!isLoadingGetDetail" ref="detailForm" :model="detailData" :rules="detailFormRules" label-colon :label-width="120">
        <Card title="基础信息" dis-hover :bordered="false">
          <Row>
            <Col span="12">
              <FormItem :label="formItemsConfig.套餐名称.label" :prop="formItemsConfig.套餐名称.prop">
                <Input
                  v-model.trim="detailData[formItemsConfig.套餐名称.prop]"
                  :disabled="detailFormConfig.isReadonly"
                  :placeholder="formItemsConfig.套餐名称.placeholder"
                  :maxlength="formItemsConfig.套餐名称.maxlength"
                />
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem :label="formItemsConfig.套餐类型.label" :prop="formItemsConfig.套餐类型.prop">
                <SelectDic
                  v-model:modelValue="detailData[formItemsConfig.套餐类型.prop]"
                  :disabled="detailFormConfig.isReadonly"
                  :placeholder="formItemsConfig.套餐类型.placeholder"
                  :dicType="formItemsConfig.套餐类型.dicType"
                />
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem :label="formItemsConfig.流量类型.label" :prop="formItemsConfig.流量类型.prop">
                <SelectDic
                  v-model:modelValue="detailData[formItemsConfig.流量类型.prop]"
                  :disabled="detailFormConfig.isReadonly"
                  :placeholder="formItemsConfig.流量类型.placeholder"
                  :dicType="formItemsConfig.流量类型.dicType"
                />
              </FormItem>
            </Col>
            <Col span="24">
              <FormItem :label="formItemsConfig.套餐说明.label" :prop="formItemsConfig.套餐说明.prop">
                <Input
                  v-model.trim="detailData[formItemsConfig.套餐说明.prop]"
                  :disabled="detailFormConfig.isReadonly"
                  :placeholder="formItemsConfig.套餐说明.placeholder"
                  type="textarea"
                  :rows="2"
                  :maxlength="formItemsConfig.套餐说明.maxlength"
                  show-word-limit
                />
              </FormItem>
            </Col>
          </Row>
        </Card>
        <Card title="资费信息" dis-hover :bordered="false">
          <Row>
            <Col span="12">
              <FormItem :label="formItemsConfig.售价.label" :prop="formItemsConfig.售价.prop">
                <Input
                  v-model.trim="detailData[formItemsConfig.售价.prop]"
                  :disabled="detailFormConfig.isReadonly"
                  :placeholder="formItemsConfig.售价.placeholder"
                  :maxlength="formItemsConfig.售价.maxlength"
                >
                  <template v-slot:append>
                    <span>元</span>
                  </template>
                </Input>
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem :label="formItemsConfig.优惠价.label" :prop="formItemsConfig.优惠价.prop">
                <Input
                  v-model.trim="detailData[formItemsConfig.优惠价.prop]"
                  :disabled="detailFormConfig.isReadonly"
                  :placeholder="formItemsConfig.优惠价.placeholder"
                  :maxlength="formItemsConfig.优惠价.maxlength"
                >
                  <template v-slot:append>
                    <span>元</span>
                  </template>
                </Input>
              </FormItem>
            </Col>
          </Row>
        </Card>
      </Form>
      <FooterToolbar>
        <template v-if="isLoadingGetDetail || detailFormConfig.isReadonly">
          <Button type="default" @click="goBack({ skipConfirmLeave: true })">返回</Button>
        </template>
        <template v-else>
          <Button type="default" @click="goBack">取消</Button>
          <Button type="primary" :loading="isLoadingCommitDetail" @click="commitDetailData">保存</Button>
        </template>
      </FooterToolbar>
    </div>
  </div>
</template>

<script>
import LoadingDetail from '@/components/loading/loading-detail.vue';
import TypicalPageHeader from '@/components/typical-page-header/index.vue';
import SelectDic from '@/components/select/select-dic.vue';

import mixinsPageForm from '@/mixins/mixinsPageForm';
import { getDemoDetail, addDemoItem, editDemoItem } from '@/api/demo';
import { isNonNullNumber } from '@/libs/lan';
import { LENGTH_MAX_INPUT, LENGTH_MAX_INPUT_LONG, RANGE_MAX_FLOAT, RANGE_MIN_FLOAT, MSG_RANGE_INTEGER, MSG_RANGE_FLOAT } from '@/define';

export default {
  mixins: [mixinsPageForm],
  components: { LoadingDetail, TypicalPageHeader, SelectDic },
  data() {
    return {
      // 通用-表单
      isLoadingGetDetail: true,
      detailData: {},
      detailFormConfig: {},
      isLoadingCommitDetail: false,
      // 页面配置-API
      pageConfigAPI: {
        getDetailData: {
          apiFun: getDemoDetail,
        },
        addDetailData: {
          apiFun: addDemoItem,
          successMsg: '示例商品添加成功',
        },
        editDetailData: {
          apiFun: editDemoItem,
          successMsg: '示例商品修改成功',
        },
      },
      // 页面配置-Button
      pageConfigButton: {
        goBack: {
          routerName: 'demo-list',
        },
      },
      // 页面配置-表单
      pageItemsConfig: {
        // --- 基础信息 ---
        套餐名称: {
          editFormItem: {
            valuekey: 'packageName',
            formRules: [{ ruleType: 'required' }, { ruleType: 'length', max: LENGTH_MAX_INPUT }],
          },
        },
        套餐类型: {
          editFormItem: {
            valuekey: 'packageType',
            formRules: [{ ruleType: 'required' }],
            dicType: 1301,
          },
        },
        流量类型: {
          editFormItem: {
            valuekey: 'flowType',
            formRules: [{ ruleType: 'required' }],
            dicType: 1307,
          },
        },
        套餐说明: {
          editFormItem: {
            valuekey: 'memo',
            formRules: [{ ruleType: 'length', max: LENGTH_MAX_INPUT_LONG }],
          },
        },
        // --- 资费信息 ---
        售价: {
          editFormItem: {
            valuekey: 'price',
            formRules: [
              { ruleType: 'required', validatorType: 'InputStrNumber' },
              { ruleType: 'float', ruleMessage: MSG_RANGE_FLOAT },
              { ruleType: 'range', min: RANGE_MIN_FLOAT, max: RANGE_MAX_FLOAT, ruleMessage: MSG_RANGE_FLOAT },
              {
                validator: (rule, value, callback) => {
                  this.$refs.detailForm.validateField('bestPrice');
                  callback();
                },
                trigger: 'change',
              },
            ],
          },
        },
        优惠价: {
          editFormItem: {
            valuekey: 'bestPrice',
            formRules: [
              { ruleType: 'float', ruleMessage: MSG_RANGE_FLOAT },
              { ruleType: 'range', min: RANGE_MIN_FLOAT, max: RANGE_MAX_FLOAT, ruleMessage: MSG_RANGE_FLOAT },
              {
                validator: (rule, value, callback) => {
                  const price = this.detailData.price;
                  if (isNonNullNumber(value) && isNonNullNumber(price)) {
                    if (Number(value) > Number(price)) {
                      callback(new Error('优惠价不能大于售价'));
                    } else {
                      callback();
                    }
                  } else {
                    callback();
                  }
                },
                trigger: 'change',
              },
            ],
          },
        },
      },
    };
  },
  created() {
    this.initDetailPage();
  },
};
</script>

<style lang="less" scoped>
@import '~@/styles/form-page.less';
</style>
