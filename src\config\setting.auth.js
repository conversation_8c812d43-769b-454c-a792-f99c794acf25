const SettingAuth = {
  // 权限组件内部，接口请求返回错误时，弹窗的类型，可选值为 Message 或 Notice
  errorModalType: 'Message',
  // 是否开启cas登录
  hasCasLogin: false,
  // 是否开启邮箱验证
  validateEmailLogin: false,
  // cas登录地址
  casLoginUrl: '',
  // cas登出
  casLogoutUrl: '',
  // cookie中token对应的key名称。默认是
  tokenName: 'Authorization',
  // 密码加密方式的publicKey，默认是
  rsaPublicKey:
    'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC0Jr1NzVUQMburkZT6Rkt0eaPmH8TN6E258l2tZMJgVCP/sL4oKjroKYmNPBkSSiLKFr9wwJqfesMeef6ChGRUXjG6DX0oxQRe0f5/UnyEm/NicJwz9xwkU34gbuo1VB/EA2QZ5dl1rj9iSsiqKLK6/QFlVuzslRdAXYZC79vprwIDAQAB',
  // 权限后端接口地址
  authAPIUrl: window.$biyiAuthAPIUrl || 'http://localhost:9004',
};
export default SettingAuth;
