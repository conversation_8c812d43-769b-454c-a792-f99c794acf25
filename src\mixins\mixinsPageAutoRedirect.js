/*
  使用示例
    mixins: [mixinsPageAutoRedirect],
    created() {
      this.openPage('/battery/portrait/dashboard');
    },
*/
import { qiankunWindow } from 'vite-plugin-qiankun/dist/helper';
export default {
  methods: {
    openPage(path, query = {}) {
      // 在新标签页打开新页面
      if (qiankunWindow.__POWERED_BY_QIANKUN__) {
        // 开启乾坤（微前端）
        // window.open(`/doiov-main/doiov-monitor${path}?fo=1`, '_blank');
        const newUrl = this.$router.resolve({
          path,
          query: {
            ...query,
            fo: 1,
          },
        });
        window.open(newUrl.href, '_blank');
      } else {
        const newUrl = this.$router.resolve({
          path,
          query,
        });
        window.open(newUrl.href, '_blank');
      }
    },
  },
};
