/**
 * 将后端返回的数据转换成 Tree 组件所需的数据
 */
export function translateTreeData({
  data,
  parentId,
  parentIds,
  parentNames,
  idAttr = 'id',
  nameAttr = 'name',
  childrenAttr = 'children',
  expandAll = false,
  nodeIdDict = {},
  nodeNamePathDict = {},
  convertItemData = () => {},
}) {
  let treeData = [];

  data.forEach((item) => {
    let _children = [];

    const id = item[idAttr];
    const name = item[nameAttr];
    const children = item[childrenAttr];

    let _parentIds = [];
    let _ids = [];
    if (parentIds && parentIds.length > 0) {
      _parentIds = [...parentIds];
      _ids = [...parentIds];
    }
    if (parentId) {
      _parentIds.push(parentId);
      _ids.push(parentId);
    }
    _ids.push(id);

    let _names = [];
    if (parentNames) {
      _names = [...parentNames];
    }
    _names.push(name);

    let _item = {
      id,
      title: name,
      expand: expandAll,
      parentIds: _parentIds,
      ids: _ids,
      names: _names,
    };

    if (children && children.length > 0) {
      const res = translateTreeData({
        data: children,
        parentId: id,
        parentIds: _parentIds,
        parentNames: _names,
        idAttr,
        nameAttr,
        childrenAttr,
        expandAll,
        nodeIdDict,
        nodeNamePathDict,
        convertItemData,
      });
      _children = res.treeData;
    }

    if (_children && _children.length > 0) {
      _item.children = _children;
    } else if (item.hasChildren) {
      _item.children = [];
    }

    convertItemData({ item: _item, orgItem: item });

    treeData.push(_item);
    nodeIdDict[id] = _item;
    nodeNamePathDict[_names.join('/')] = _item;
  });

  return { treeData, nodeIdDict, nodeNamePathDict };
}

/**
 * 将后端返回的数据转换成 Cascader 组件所需的数据
 */
export function translateTreeDataToCascader({
  data,
  parentId,
  idAttr = 'id',
  nameAttr = 'name',
  childrenAttr = 'children',
  convertItemData = () => {},
  skipChildren = () => false,
}) {
  let cascaderData = [];
  let parentIdDict = {};

  data.forEach((item) => {
    let _children = [];
    const id = item[idAttr];
    const name = item[nameAttr];
    const children = item[childrenAttr];

    let _item = {
      value: id,
      label: name,
    };
    parentIdDict[id] = parentId;

    if (skipChildren({ item: _item, orgItem: item })) {
      return;
    }

    if (children && children.length > 0) {
      const res = translateTreeDataToCascader({ data: children, parentId: id, idAttr, nameAttr, childrenAttr, convertItemData, skipChildren });
      _children = res.cascaderData;
    }

    if (_children && _children.length > 0) {
      _item.children = _children;
    } else if (item.hasChildren) {
      _item.children = [];
    }

    convertItemData({ item: _item, orgItem: item });

    cascaderData.push(_item);
  });

  return { cascaderData, parentIdDict };
}
