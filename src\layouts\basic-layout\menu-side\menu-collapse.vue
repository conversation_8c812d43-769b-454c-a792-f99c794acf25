<template>
  <Dropdown :transfer="false" placement="right-start" :class="dropdownClasses">
    <li v-if="topLevel" :class="menuItemClasses">
      <i-menu-side-title :menu="menu" hide-title collapse />
    </li>
    <DropdownItem v-else>
      <i-menu-side-title :menu="menu" :selected="openNames.indexOf(menu.path) >= 0" collapse />
      <Icon type="ios-arrow-forward" class="i-layout-menu-side-arrow" />
    </DropdownItem>
    <template v-slot:list>
      <DropdownMenu>
        <div v-if="showCollapseMenuTitle" class="i-layout-menu-side-collapse-title">
          <i-menu-side-title :menu="menu" collapse />
        </div>
        <template v-for="(item, index) in menu.children">
          <i-link v-if="item.children === undefined || !item.children.length" :to="item.path" :target="item.target" @click="handleClick(item.path)">
            <DropdownItem
              :divided="item.divided"
              :class="{
                'i-layout-menu-side-collapse-item-selected': item.path === activePath,
              }"
            >
              <i-menu-side-title :menu="item" collapse />
            </DropdownItem>
          </i-link>
          <i-menu-side-collapse v-else :menu="item" />
        </template>
      </DropdownMenu>
    </template>
  </Dropdown>
</template>

<script>
import { mapState } from 'vuex';
import clickItem from '../mixins/click-item';
import iMenuSideTitle from './menu-title';

export default {
  name: 'IMenuSideCollapse',
  components: { iMenuSideTitle },
  mixins: [clickItem],
  props: {
    menu: {
      type: Object,
      default() {
        return {};
      },
    },
    // 是否是第一级，区分在于左侧和展开侧
    topLevel: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    ...mapState('admin/layout', ['siderTheme', 'showCollapseMenuTitle']),
    ...mapState('admin/menu', ['activePath', 'openNames']),
    dropdownClasses() {
      return {
        'i-layout-menu-side-collapse-top': this.topLevel,
        'i-layout-menu-side-collapse-dark': this.siderTheme === 'dark',
      };
    },
    menuItemClasses() {
      return [
        'ivu-menu-item i-layout-menu-side-collapse-top-item',
        {
          'ivu-menu-item-selected ivu-menu-item-active': this.openNames.includes(this.menu.path), // -active 在高亮时，有背景
        },
      ];
    },
  },
};
</script>
