/**
 * 持久化存储
 * 一般情况下，您无需修改此文件
 * */
import { cloneDeep } from 'lodash';

/**
 * @description 检查路径是否存在 不存在的话初始化
 * @param {Object} dbName {String} 数据库名称
 * @param {Object} path {String} 路径
 * @param {Object} user {Boolean} 区分用户
 * @param {Object} validator {Function} 数据校验钩子 返回 true 表示验证通过
 * @param {Object} defaultValue {*} 初始化默认值
 * @returns {String} 可以直接使用的路径
 */
function pathInit({ dbName = 'database', path = '', user = true, validator = () => true }) {
  // const uuid = util.cookies.get('uuid') || 'ghost-uuid'
  // 暂时固定
  const uuid = 'ghost-uuid';
  const currentPath = `${dbName}.${user ? `user.${uuid}` : 'public'}${path ? `.${path}` : ''}`;
  return currentPath;
}

export { pathInit };

export default {
  namespaced: true,
  actions: {
    /**
     * @description 将数据存储到指定位置 | 路径不存在会自动初始化
     * @description 效果类似于取值 dbName.path = value
     * @param context context
     * @param {Object} dbName {String} 数据库名称
     * @param {Object} path {String} 存储路径
     * @param {Object} value {*} 需要存储的值
     * @param {Object} user {Boolean} 是否区分用户
     */
    set(context, { dbName = 'database', path = '', value = '', user = false }) {
      const key = pathInit({
        dbName,
        path,
        user,
        context,
      });
      const menuValue = JSON.stringify(value);
      sessionStorage.setItem(key, menuValue);
    },
    /**
     * @description 获取数据
     * @description 效果类似于取值 dbName.path || defaultValue
     * @param context context
     * @param {Object} dbName {String} 数据库名称
     * @param {Object} path {String} 存储路径
     * @param {Object} defaultValue {*} 取值失败的默认值
     * @param {Object} user {Boolean} 是否区分用户
     */
    get(context, { dbName = 'database', path = '', defaultValue = '', user = false }) {
      const key = pathInit({
        dbName,
        path,
        user,
      });
      const value = cloneDeep(JSON.parse(sessionStorage.getItem(key)));
      return new Promise((resolve) => {
        resolve(value);
      });
    },
  },
};
