<template>
  <div>
    <component v-bind:is="type" v-bind="$attrs" :modelValue="modelValue" @getVerifyRes="getVerifyRes" @choseCaptcha="choseCaptcha"></component>
  </div>
</template>

<script>
import { setAxiosConfigs } from '../request/index.js';
import digitalCaptcha from './components/digital.vue';
import dragCaptcha from './components/drag.vue';
import emailCaptcha from './components/email.vue';
export default {
  name: 'biyiCaptcha',

  components: {
    digitalCaptcha,
    dragCaptcha,
    emailCaptcha,
  },

  props: {
    type: {
      type: String,
      validator: (value) => ['digitalCaptcha', 'dragCaptcha', 'emailCaptcha'].includes(value),
    },
    modelValue: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      headerConfig: null,
    };
  },

  created() {
    setAxiosConfigs({
      baseURL: this.$attrs.baseUrl,
    });
    this.addWatcher();
  },

  methods: {
    /**
     * @description: 向父组件传递验证码数据, 并为暴露出的请求实例设置请求头
     * @param {object} data 验证码数据，格式为 { key: xx, value: xx }
     */
    getVerifyRes(data) {
      this.setServiceConfig(data);
      this.$emit('getVerifyRes', data);
    },
    /**
     * @description: 关闭验证码弹窗时，向父组件传递数据
     * @param {boolen} show 弹窗状态
     */
    choseCaptcha(show) {
      this.$emit('update:modelValue', show);
    },
    /**
     * @description: 为暴露的axios实例设置请求头
     * @param {object} data  验证码数据，格式为 { key: xx, value: xx }
     */
    setServiceConfig(data) {
      const config = {
        headers: {
          biyiCaptchaKey: data.key,
          biyiCaptcha: data.value || '',
        },
      };
      this.headerConfig = config;
      setAxiosConfigs(config);
    },
    /**
     * @description: 根据验证码类型决定是否需要添加监听器，监听器会为暴露的axios实例设置请求头
     */
    addWatcher() {
      if (this.type === 'digitalCaptcha') {
        this.$watch('$attrs.digitalValue', (newVal, oldVal) => {
          if (newVal.length === 4) {
            this.headerConfig.headers.biyiCaptcha = newVal;
            setAxiosConfigs(this.headerConfig);
          }
        });
      }
    },
  },
  emits: ['getVerifyRes', 'update:modelValue'],
};
</script>
