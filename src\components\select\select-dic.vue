<template>
  <div>
    <Select v-model="value" :placeholder="placeholder" :clearable="clearable" :disabled="disabled" :multiple="multiple" @on-change="changedata">
      <Option v-for="(item, index) in optionlist" :key="index" :value="item.value">{{ item.label }}</Option>
    </Select>
  </div>
</template>
<script>
import { getDicData } from '@/api/common';
import { isArray } from '@/libs/lan';

export default {
  props: {
    modelValue: {
      type: [String, Number, Array],
      default: '',
    },
    spliceOption: {
      type: Array,
      default: function () {
        return [];
      },
    },
    dicType: {
      type: [String, Number],
      default: '',
    },
    placeholder: {
      type: String,
      default: '',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    clearable: {
      type: Boolean,
      default: true,
    },
    multiple: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      value: '',
      optionlist: [],
    };
  },
  watch: {
    modelValue: {
      deep: true,
      immediate: true,
      handler: function (val) {
        if (isArray(val)) {
          this.value = val;
        } else {
          this.value = `${val}`;
        }
      },
    },
  },
  created() {
    if (this.dicType) {
      this.getOptionList();
    }
  },
  methods: {
    getOptionList() {
      getDicData({ dicType: this.dicType }).then((response) => {
        if (this.spliceOption) {
          response = response.filter((item) => {
            return !this.spliceOption.includes(item.value);
          });
        }
        this.optionlist = response;
      });
    },
    changedata(val) {
      this.$emit('update:modelValue', val);
      this.$emit('change-data', val);
    },
  },
  emits: ['update:modelValue', 'change-data'],
};
</script>
