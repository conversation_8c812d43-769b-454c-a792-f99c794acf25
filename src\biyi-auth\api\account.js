import service from '../http/http';
import util from '../libs/util';
import { userStore } from '../views/store';

// 登录
export function getLogin(params) {
  const data = params.data;
  return service({
    url: '/api/system/login',
    method: 'post',
    data,
    headers: params.headers,
  });
}

// 获取综合菜单和用户信息
export function getUserAll(type) {
  const arr = [getMenus(type), getUserInfo(type)];
  return Promise.all(arr).then((res) => {
    userStore.set(res);
    return res;
  });
}

// 获取用户菜单
export function getMenus(type) {
  return service({
    url: '/api/user-permissions?account=current',
    method: 'get',
    params: {
      type,
    },
  }).then((res) => {
    return { menus: util.account.buildMenuTree(res, type), tree: res };
  });
}

// 获取用户信息
export function getUserInfo(type) {
  return service({
    url: '/api/users?account=current',
    method: 'get',
    params: {
      type,
    },
  }).then((res) => {
    return res;
  });
}

// 获取个人中心基本信息
export function getPersonInfo() {
  return service({
    url: '/api/user-details?account=current',
    method: 'get',
  });
}

// 修改个人中心基础信息
export function updatePersonInfo(data) {
  return service({
    url: '/api/user-details?account=current',
    method: 'put',
    data,
  });
}

// 修改密码
export function resetPersonPassword(data) {
  return service({
    url: '/api/passwords?action=modify',
    method: 'put',
    data,
  });
}

// 获取用户是否开启双重认证
export function isDoubleAuthentication(params) {
  return service({
    url: '/api/verify-types',
    method: 'get',
    params,
  });
}
