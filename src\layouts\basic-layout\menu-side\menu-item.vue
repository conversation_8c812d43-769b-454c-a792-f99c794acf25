<template>
  <div>
    <MenuItem :to="menu.path" :replace="menu.replace" :target="menu.target" :name="menu.path" @click="handleClick(menu.path)">
      <i-menu-side-title :menu="menu" :hide-title="hideTitle" />
      <Badge v-bind="badgeData" v-if="badgeData" class="i-layout-menu-side-badge" />
    </MenuItem>
  </div>
</template>

<script>
import clickItem from '../mixins/click-item';
import menuBadge from '../mixins/menu-badge';
import iMenuSideTitle from './menu-title';

export default {
  name: 'IMenuSideItem',
  components: { iMenuSideTitle },
  mixins: [clickItem, menuBadge],
  props: {
    menu: {
      type: Object,
      default() {
        return {};
      },
    },
    hideTitle: {
      type: Boolean,
      default: false,
    },
  },
};
</script>
