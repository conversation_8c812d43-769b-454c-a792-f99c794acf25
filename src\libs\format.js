import { isNonNullNumber } from './lan.js';

// ------------------ 数字 ------------------
/**
 * 格式化数字的显示，固定保留几位小数
 * precision：需要保留的精度
 */
export function fixedNumber(value, precision) {
  if (isNonNullNumber(value)) {
    return Number(value).toFixed(precision);
  } else {
    return value;
  }
}

export function formatByte(val, precision) {
  if (isNonNullNumber(val)) {
    let _val = Number(val);
    if (_val >= 1024) {
      return {
        num: fixedNumber(_val / 1024, precision),
        unit: 'G',
      };
    } else {
      return {
        num: fixedNumber(_val, precision),
        unit: 'M',
      };
    }
  } else {
    return {
      num: '-',
      unit: '',
    };
  }
}
export function formatByteStr(...args) {
  const { num, unit } = formatByte(...args);
  return num + unit;
}

// ------------------ 百分比 ------------------

export function setPercent(args = {}) {
  const { ary = [], percentAttr = 'percent', numAttr = 'num' } = args;

  let totalNum = 0;
  const _ary = ary.map((item) => {
    const num = Number(item[numAttr]);
    totalNum += num;
    return {
      ...item,
      [numAttr]: num,
    };
  });

  let totalPercent = 0;
  _ary.forEach((item, index) => {
    if (index === _ary.length - 1) {
      item[percentAttr] = 100 - totalPercent;
    } else {
      const percent = fixedNumber((item[numAttr] / totalNum) * 100, 0);
      const _percent = Number(percent);
      totalPercent += _percent;
      item[percentAttr] = _percent;
    }
  });

  return _ary;
}

// ------------------ 时间 ------------------

/**
 * 时间戳转时间
 */
export function timestampToTime(date, state) {
  if (!date) {
    return '';
  }
  // var date = new Date(timestamp);
  let Y = date.getFullYear();
  let M = (date.getMonth() + 1).toString().padStart(2, 0);
  let D = date.getDate().toString().padStart(2, 0);
  let h = date.getHours().toString().padStart(2, 0);
  let m = date.getMinutes().toString().padStart(2, 0);
  let s = date.getSeconds().toString().padStart(2, 0);
  return state ? `${Y}-${M}-${D} ${h}:${m}:${s}` : `${Y}-${M}-${D}`;
}

export function timestampToMonth(date, type) {
  if (!date) {
    return '';
  }
  if (!date.getFullYear) {
    return date;
  }
  let Y = date.getFullYear();
  let M = (date.getMonth() + 1).toString().padStart(2, 0);
  return `${Y}-${M}`;
}
