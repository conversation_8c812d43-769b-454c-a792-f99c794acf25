server {
     listen 80;
     server_name localhost;
     root /usr/share/nginx/html; 

     location /doiov-ivsmp {
        alias  /usr/share/nginx/html/doiov-ivsmp/;
        try_files $uri $uri/ /doiov-ivsmp/index.html;
        index index.html index.htm;
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
        add_header Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';
    }
    
     error_page 500 502 503 504 /50x.html;
     location = /50x.html {
         root html;
     }
}
