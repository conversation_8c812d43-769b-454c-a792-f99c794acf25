import { throttle } from '@/libs/lan.js';
import { getGeoJson, getRegion, getParentRegion, getRegionInfo } from './geoJson/index';

export default {
  props: {
    chartWidth: {
      type: Number,
      default: 600,
    },
    chartHeight: {
      type: Number,
      default: 400,
    },
  },
  computed: {
    options: function () {
      const geo = {
        top: this.currentRegionCode === '100000' ? 200 : 60,
        zoom: this.currentRegionCode === '100000' ? 1.6 : 1,
        map: this.mapName,
        itemStyle: {
          borderColor: '#A1F5F6',
          borderWidth: 3.5,
        },
        emphasis: {
          show: false,
        },
      };

      // 用十层阴影来做出立体地图效果
      let geoList = [];
      for (let index = 0; index < 10; index++) {
        geoList.push({
          ...geo,
          zlevel: 0,
          label: {
            show: false,
          },
          itemStyle: {
            // shadowColor: '#0093D3',
            shadowColor: '#A1F5F6',
            shadowOffsetY: index * 1.5,
            shadowOffsetX: 0,
          },
        });
      }

      let series = [
        {
          type: 'map',
          map: this.mapName,
          ...geo,
          label: {
            show: true,
            // formatter: '{mapLabel|{b}}{mapVal|{@value}}{mapBg|}',
            formatter: (params) => {
              const { mapLabel, mapVal } = params.data || {};
              if (mapVal) {
                return `{mapLabel|${mapLabel}}{mapVal|${mapVal}}{mapBg|}`;
              } else {
                return '';
              }
            },
            rich: {
              mapVal: {
                color: '#00FFFF',
                fontSize: 16,
                fontFamily: 'YouSheBiaoTiHei',
                // padding: [0, 0, 0, 7],
              },
              mapLabel: {
                color: '#FFFFFF',
                fontSize: 16,
                fontFamily: 'YouSheBiaoTiHei',
                width: 50,
                align: 'right',
              },
              mapBg: {
                // backgroundColor: '#1975D3',
                // backgroundColor: new this.$echarts.graphic.RadialGradient(0.5, 0.5, 0.5, [
                //   { offset: 0, color: 'rgba(203,105,19, 1)' },
                //   { offset: 1, color: 'rgba(203,105,19, 0)' },
                // ]),
                width: '100%',
                height: 54,
                align: 'right',
                borderRadius: 20,
              },
            },
          },
          itemStyle: {
            normal: {
              areaColor: '#D6DAF6',
              borderWidth: 1.5,
              borderColor: '#5ECDE8',
            },
            emphasis: {
              areaColor: '#ff9900',
              borderWidth: 0,
            },
          },
          // emphasis: {
          //   label: {
          //     show: false,
          //   },
          // },
          select: {
            disabled: true,
          },
          data: this.mapData.serieData,
        },
      ];

      return {
        tooltip: {
          show: false,
          // show: this.currentRegionCode !== '100000',
          // triggerOn: 'click',
          // padding: 0,
          // backgroundColor: 'rgba(50,50,50,0)',
          // formatter: this.formatterMapTooltip,
        },
        visualMap: {
          show: false,
          min: 1,
          max: 34,
          inRange: {
            color: ['#0260a0', '#3ca4e5', '#38BDea', '#93D2F5', '#5ECDE8'],
          },
          seriesIndex: 0,
        },
        geo: [...geoList],
        series,
      };
    },
    mapStyle: function () {
      return {
        width: `${this.chartWidth}px`,
        height: `${this.chartHeight}px`,
      };
    },
  },
  watch: {
    chartWidth: {
      handler: function () {
        this.throttleResizeEchart();
      },
    },
    chartHeight: {
      handler: function () {
        this.throttleResizeEchart();
      },
    },
  },
  mounted() {
    // this.init();
  },
  beforeUnmount() {
    if (this.mycharts) {
      this.mycharts.dispose();
      this.mycharts = null;
    }
  },
  methods: {
    init() {
      // this.mycharts = this.$echarts.init(this.$refs.chratMap);
      this.initEcharts(this.currentRegionCode, this.currentRegionName);
    },
    async initEcharts(_regionCode, _regionName) {
      try {
        const geoJson = await getGeoJson(_regionCode);
        this.$echarts.registerMap(this.mapName, geoJson);

        this.updateEchart();

        this.mycharts.off('click');
        if (this.currentRegionCode === '100000') {
          this.mycharts.on('click', (params) => {
            params.event.event.stopPropagation();

            if (!params.name) {
              return;
            }

            if (!params.data || !params.data.mapVal) {
              this.$Message.warning('该省份暂无车辆');
              return;
            }

            const { regionCode, regionName } = getRegion(params.name);
            if (!this.mapData.regionDict[regionName]) {
              this.$Message.warning('暂无数据');
              return;
            }

            this.isLoadingMap = true;
            this.currentRegionCode = `${regionCode}`;
            this.currentRegionName = regionName;
            setTimeout(() => {
              this.initEcharts(regionCode, regionName);
            }, 10);
          });
        } else
          this.mycharts.on('click', (params) => {
            params.event.event.stopPropagation();

            if (params.name) {
              if (params.data && params.data.mapVal) {
                if (this.clickedRegionCode !== params.data.regionCode) {
                  this.clickedRegionCode = params.data.regionCode;

                  const regionData = this.mapData.regionDict[params.data.regionName] || {};
                  this.clickedRegionData = {
                    ...regionData,
                    offsetX: params.event.offsetX,
                    offsetY: params.event.offsetY,
                  };

                  if (regionData.vehicleAlarm) {
                    this.clickedRegionVin.type = 'alarm';
                  } else if (regionData.batteryWarning) {
                    this.clickedRegionVin.type = 'warning';
                  } else {
                    this.clickedRegionVin.type = 'alarm';
                  }

                  return;
                }
              } else {
                this.$Message.warning('该区域暂无车辆数据');
              }
            }

            this.hideRegionModal();
          });

        this.isLoadingMap = false;
      } catch (error) {
        this.isLoadingMap = false;
        this.$Message.error(error.message);
      }
    },
    updateEchart() {
      // 全国地图和省份地图的 zoom 不一样，导致用 geo 伪装成 3D 地图的时候，从全国地图切换到省份地图的时候，会有一瞬间闪过2个地图。所以改成先 dispose 后，再 init
      if (this.mycharts) {
        this.mycharts.dispose();
      }
      this.mycharts = this.$echarts.init(this.$refs.chratMap);

      this.mycharts.setOption(this.options, true);
    },
    resizeEchart() {
      setTimeout(() => {
        if (this.mycharts) {
          this.mycharts.resize();
        }
      }, 10);
    },
    throttleResizeEchart: throttle(function () {
      this.resizeEchart();
    }, 100),
    goBackMap() {
      const { regionCode, regionName } = getParentRegion(this.currentRegionCode);
      if (regionCode) {
        this.isLoadingMap = true;
        this.currentRegionCode = `${regionCode}`;
        this.currentRegionName = regionName;
        this.hideRegionModal();
        setTimeout(() => {
          this.initEcharts(regionCode, regionName);
        }, 10);
      }
    },
    abbreviatedRegionName(regionName) {
      if (regionName === null || regionName === undefined || regionName === '') {
        return '-';
      } else {
        switch (regionName) {
          case '内蒙古自治区':
            return '内蒙古';
          case '广西壮族自治区':
            return '广西';
          case '西藏自治区':
            return '西藏';
          case '宁夏回族自治区':
            return '宁夏';
          case '新疆维吾尔自治区':
            return '新疆';
          case '香港特别行政区':
            return '香港';
          case '澳门特别行政区':
            return '澳门';
          default:
            // 删去“省”、“市”
            return regionName.substr(0, regionName.length - 1);
        }
      }
    },
    getRegionInfo(regionCode) {
      return getRegionInfo(regionCode);
    },
    formatterMapTooltip(params) {
      return params.name;
    },
    hideRegionModal() {
      this.clickedRegionCode = '';
      this.clickedRegionData = {};
      this.clickedRegionVin = {
        type: '',
        isExpand: false,
      };
    },
  },
};
