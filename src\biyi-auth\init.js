// 相关页面路由
// import RegisterRoute from './routes/register'
import LoginRoute from './routes/login';
// import ForgetPassword from './routes/forget-password'
import personalRoute from './routes/personal';
// import UpdatePassword from './routes/update-password'
// import UserLocked from './routes/user-locked'

import { userStore } from './views/store';
import { getUserAll } from './api/account';

const authUtil = {};

// 路由配置
authUtil.getInit = function (options) {
  // [MEMO VU3升级] 暂时只搬运 登录 和 个人中心 页面
  // return { RegisterRoute, LoginRoute, ForgetPassword, personalRoute, UpdatePassword, UserLocked }
  return { LoginRoute, personalRoute };
};

// 获取当前用户信息
// token
// 菜单
// 鉴权
authUtil.getUser = function () {};

// 暴露登录信息（token，菜单，权限数据）
authUtil.accountLogin = function () {
  return new Promise((resolve) => {
    resolve(userStore.userInfos);
  });
};

authUtil.accountRefresh = function (type) {
  return new Promise((resolve) => {
    resolve(getUserAll(type));
  });
};

export default authUtil;
