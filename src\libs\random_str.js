// 生成随机字符串
export default function (len = 32) {
  const $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890';
  const maxPos = $chars.length;
  let str = '';
  for (let i = 0; i < len; i++) {
    str += $chars.charAt(Math.floor(randomFloat() * maxPos));
  }
  return str;
}

function randomFloat() {
  const fooArray = new Uint32Array(1);
  const maxUint32 = 0xffffffff;
  const rdm = crypto.getRandomValues(fooArray)[0] / maxUint32;
  return rdm;
}
