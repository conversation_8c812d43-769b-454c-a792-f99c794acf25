/*
  使用示例
    mixins: [mixinsTable],
    data() {
      return {
        // 通用-搜索
        searchFormInitData: {},
        searchParams: {},
        // 通用-列表
        listLoading: true,
        listDataSelf: {
          listData: [],
        },
        // 组件配置-列表、搜索
        searchFormListOrder: ['套餐名称'],
        listColumnsOrder: ['CHECKBOX', '套餐名称', 'ACTION'],
        componentItemsConfig: {
        },
      };
    },
    created() {
      this.initSearchFormData();
    },

    searchFormItem 的示例见 createSearchForm.js
    listColumn 的示例见 createListColumns.js
*/
import { createSearchFormlist, getSearchFormData } from './createSearchForm.js';
import { createListColumns } from './createListColumns.js';
import { forEach } from '@/libs/lan';

export default {
  computed: {
    /**
     * 根据 componentItemsConfig 的配置，生成列表的 columns
     */
    listColumns: function () {
      const listColumns = createListColumns({
        listColumnsOrder: this.listColumnsOrder,
        pageItemsConfig: this.componentItemsConfig,
      });

      // 序号列
      const iINDEX = this.listColumnsOrder.indexOf('INDEX');
      if (iINDEX !== -1) {
        listColumns[iINDEX].indexMethod = (row, index) => {
          return row._index + 1;
        };
      }

      return listColumns;
    },
    /**
     * 根据 componentItemsConfig 的配置，生成搜索的 columns
     */
    searchFormItems: function () {
      return createSearchFormlist({
        pageItemsConfig: this.componentItemsConfig,
      });
    },
    searchFormItemsPropDict: function () {
      let searchFormItemsDict = {};
      forEach(this.searchFormItems, (item, key) => {
        if (item.prop) {
          searchFormItemsDict[item.prop] = item;
        }
      });
      return searchFormItemsDict;
    },
    /**
     * 根据搜索条件过滤后显示在列表里的值
     */
    listDataFiltered: function () {
      this.listLoading = true;

      let listDataFiltered = [];
      this.listDataSelf.listData.forEach((item, index) => {
        let isMatch = true;
        const keys = Object.keys(this.searchParams);
        for (let i = 0; i < keys.length; i++) {
          const key = keys[i];

          if (this.searchParams[key]) {
            if (this.searchFormItemsPropDict[key].searchType === 'fuzzy') {
              // 模糊搜索
              if (!item[key].includes(this.searchParams[key])) {
                isMatch = false;
                break;
              }
            } else if (item[key] !== this.searchParams[key]) {
              isMatch = false;
              break;
            }
          }
        }
        if (isMatch) {
          listDataFiltered.push(item);
        }
      });

      this.listLoading = false;

      return listDataFiltered;
    },
  },
  methods: {
    /**
     * 刷新页面数据
     */
    async refreshList() {
      this.searchParams = this.getSearchParams();
    },
    setListData(listData) {
      this.listDataSelf['listData'] = [...listData];
    },

    /**
     * 设置搜索项的初始值
     */
    initSearchFormData() {
      this.searchFormInitData = {};
      this.searchFormListOrder.forEach((key) => {
        if (this.searchFormItems[key]) {
          const { prop, initValue, formItemType } = this.searchFormItems[key];
          if (initValue !== null && initValue !== undefined) {
            this.searchFormInitData[prop] = initValue;
          } else {
            if (formItemType === 'DateRangeSeparate') {
              this.searchFormInitData[prop] = {};
            }
          }
        }
      });
    },
    /**
     * 重置搜索
     */
    handleReset() {
      this.refreshList();
    },

    /**
     * 搜索
     */
    handleSearch() {
      this.refreshList();
    },

    /**
     * 获取搜索项的值
     */
    getSearchParams() {
      let searchFormData = this.searchFormInitData || {};
      if (this.$refs.searchForm) {
        searchFormData = this.$refs.searchForm.getSearchFormData();
      }
      return getSearchFormData({
        searchFormItems: this.searchFormItems,
        searchFormData,
      });
    },
  },
};
