import { URL_UPLOAD_FILE } from '@/define';
import { findRight, removeRight } from '@/libs/lan';
import util from '@/libs/util';

export default {
  props: {
    initValue: {
      type: Array,
      default: function () {
        return [];
      },
    },
    imgSize: {
      type: Number,
      default: 98,
    },
    suffixMsg: {
      type: String,
    },
    disabledUpload: {
      type: <PERSON>olean,
      default: false,
    },
    uploadFileUrl: {
      type: String,
    },
    uploadFileMax: {
      type: Number,
      default: 5,
    },
    uploadFileMaxSize: {
      type: Number,
      default: 10, // 文件小于10M
    },
    uploadFileMaxSizeUnit: {
      type: String,
      default: 'MB',
    },
    uploadAcceptFileType: {
      // https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input/file#unique_file_type_specifiers
      type: String,
    },
    // uploadFileInfo: {
    //   // 用于说明上传文件用于哪个功能
    //   type: String,
    // },
    setUploadFileHeader: {
      type: Function,
      default: function () {
        return {};
      },
    },
    uploadErrMsg: {
      type: String,
      default: '上传失败',
    },
    uploadFormatError: {
      type: String,
      default: '文件格式不对',
    },
  },
  computed: {
    uploadAction: function () {
      return this.uploadFileUrl || URL_UPLOAD_FILE;
    },
    uploadAxiosHeaders: function () {
      const token = util.cookies.get('token');
      const header = this.setUploadFileHeader();
      return {
        Authorization: token,
        // 'File-Type': encodeURI(this.uploadFileInfo),
        ...header,
      };
    },
    hasRemove: function () {
      if (this.disabledUpload) {
        return false;
      }

      return true;
    },
    isShowUpload: function () {
      if (this.disabledUpload) {
        return false;
      }

      if (this.uploadFileMax) {
        if (this.uploadFileList.length >= this.uploadFileMax) {
          return false;
        }
      }
      return true;
    },
    acceptFileType: function () {
      return this.uploadAcceptFileType;
    },
  },
  methods: {
    beforeUpload(file) {
      this.isUploading = true;
      console.log('beforeUpload: ', file.type);
      if (this.acceptFileType) {
        const acceptFileTypeAry = this.acceptFileType.split(',').map((item) => {
          return item.trim();
        });
        const isType = acceptFileTypeAry.includes(file.type);
        if (!isType) {
          this.$Message.warning(this.uploadFormatError);
          this.isUploading = false;
          return false;
        }
      }

      if (this.uploadFileList && this.uploadFileList.length >= this.uploadFileMax) {
        this.$Message.warning(`最多上传${this.uploadFileMax}个文件`);
        this.isUploading = false;
        return false;
      }

      let checkSize;
      if (this.uploadFileMaxSizeUnit === 'MB') {
        checkSize = file.size / 1024 / 1024 < this.uploadFileMaxSize;
      } else if (this.uploadFileMaxSizeUnit === 'KB') {
        checkSize = file.size / 1024 < this.uploadFileMaxSize;
      }
      if (this.uploadFileMaxSize && !checkSize) {
        this.$Message.warning(`上传文件大小不能超过${this.uploadFileMaxSize}${this.uploadFileMaxSizeUnit}`);
        this.isUploading = false;
        return false;
      }

      // true: 使用 action 地址自动上传; false: 不使用 action 的地址，而是手动上传：
      return true;
      /*
      this.uploadFile(file);
      return false;
      */
    },
    handleSuccess(response, file) {
      const { fileId, fileSrvAddr } = response.data || {};

      this.uploadFileList.push({
        ...file,
        filePath: fileId,
        fileUrl: fileSrvAddr,
      });

      this.$emit('on-change-filelist', {
        fileList: this.uploadFileList,
      });

      this.isUploading = false;
    },
    handleError(e, response, file) {
      // iview 的文档中说 on-error 的参数是 error, file, fileList，但是，其实是 error, response, file

      this.$Message.error({
        content: this.uploadErrMsg,
      });

      this.isUploading = false;
    },
    handleRemove(filePath) {
      removeRight(this.uploadFileList, (elem) => elem.filePath === filePath);

      this.$emit('on-change-filelist', {
        fileList: this.uploadFileList,
      });
    },
    onRemove(file) {
      this.handleRemove(file.filePath);
    },
  },
  mounted() {
    this.uploadFileList = this.initValue || [];
  },
  emits: ['on-change-filelist'],
};
