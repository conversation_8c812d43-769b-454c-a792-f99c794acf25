/**
 * meta
 *  auth   路由对应的权限码
 *  title  页面名（ this.pageTitle 默认取这里的值来显示 ）
 *  requiredId    true: 如果路由参数中缺少id，弹出warning，并且返回上一级页面
 *  confirmLeave  true: 离开页面前弹出是否离开的提示消息
 */
export default [
  // ------------------ 有增删改查功能的列表页面 ------------------
  {
    path: '/demo/demo-list',
    name: 'demo-list',
    meta: {
      auth: 'monitor.demo.demo-list',
    },
    component: () => import('@/views/demo/demo-list'),
  },
  {
    path: '/demo/demo-list/detail',
    name: 'demo-list-detail',
    meta: {
      auth: 'monitor.demo.demo-list.detail',
      title: '查看示例商品',
      requiredId: true,
    },
    component: () => import('@/views/demo/demo-list/detail.vue'),
  },
  {
    path: '/demo/demo-list/add',
    name: 'demo-list-add',
    meta: {
      auth: 'monitor.demo.demo-list.add',
      title: '添加示例商品',
      confirmLeave: true,
    },
    component: () => import('@/views/demo/demo-list/detail.vue'),
  },
  {
    path: '/demo/demo-list/edit',
    name: 'demo-list-edit',
    meta: {
      auth: 'monitor.demo.demo-list.edit',
      title: '修改示例商品',
      requiredId: true,
      confirmLeave: true,
    },
    component: () => import('@/views/demo/demo-list/detail.vue'),
  },
];
