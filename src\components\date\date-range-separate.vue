<template>
  <div class="date-range-wrap">
    <DatePicker
      v-model="dateBegin"
      :type="type"
      :format="format"
      :options="startOptions"
      :placeholder="placeholderBeginTime"
      :style="styleDatePicker"
      transfer
      :editable="false"
      @on-change="onStartChange"
    />
    <div style="margin: 0px 4px">-</div>
    <DatePicker
      v-model="dateEnd"
      :type="type"
      :format="format"
      :options="endOptions"
      :placeholder="placeholderEndTime"
      :style="styleDatePicker"
      transfer
      :editable="false"
      @on-change="onEndChange"
    />
  </div>
</template>
<script>
export default {
  props: {
    modelValue: {
      type: Object,
      default: function () {
        return {};
      },
    },
    type: {
      type: String,
      default: 'date',
    },
    format: {
      type: String,
      default: 'yyyy-MM-dd',
    },
    placeholderBeginTime: {
      type: String,
      default: '请选择',
    },
    placeholderEndTime: {
      type: String,
      default: '请选择',
    },
    notAllowFutureBegin: {
      type: Boolean,
      default: false,
    },
    earliest: {
      type: String,
      default: '',
    },
    rangeLimited: {
      type: Number,
      default: 0, // 0 的话，不校验
    },
  },
  data() {
    return {
      dateBegin: '',
      valueBegin: '',
      dateEnd: '',
      valueEnd: '',
    };
  },
  computed: {
    styleDatePicker: function () {
      let style = {};
      if (this.type === 'date' || this.type === 'month') {
        style.width = '120px';
      } else if (this.type === 'datetime') {
        style.width = '180px';
      }

      return style;
    },
    startOptions: function () {
      return {
        disabledDate: (date) => {
          const dateValue = this.valueOfDate(date);

          if (this.notAllowFutureBegin && dateValue > this.valueOfDate(new Date())) {
            return true;
          }
          if (this.earliest) {
            if (dateValue < this.valueOfDate(new Date(this.earliest))) {
              return true;
            }
          }
          if (this.dateEnd) {
            const dateEndValue = this.valueOfDate(this.dateEnd);
            if (dateValue > dateEndValue) {
              return true;
            }
            if (this.rangeLimited && dateEndValue - dateValue > this.rangeLimited * 86400000) {
              return true;
            }
          }

          return false;
        },
      };
    },
    endOptions: function () {
      return {
        disabledDate: (date) => {
          if (this.dateBegin) {
            const dateValue = this.valueOfDate(date);
            const dateBeginValue = this.valueOfDate(this.dateBegin);
            if (dateValue < dateBeginValue) {
              return true;
            }
            if (this.rangeLimited && dateValue - dateBeginValue > this.rangeLimited * 86400000) {
              return true;
            }
          }
          return false;
        },
      };
    },
  },
  watch: {
    modelValue: {
      deep: true,
      immediate: true,
      handler: function (val = {}) {
        const valueBegin = val.valueBegin || '';
        this.dateBegin = valueBegin === '' ? '' : new Date(valueBegin);
        this.valueBegin = valueBegin;

        const valueEnd = val.valueEnd || '';
        this.dateEnd = valueEnd === '' ? '' : new Date(valueEnd);
        this.valueEnd = valueEnd;
      },
    },
  },
  methods: {
    onStartChange(value) {
      // this.dateBegin 是 Date 型的数值，例如 Tue May 09 2023 00:00:00 GMT+0800 (中国标准时间)
      // value 是已经格式化后的数值，例如 2023-05-09
      this.valueBegin = value;
      this.$emit('on-change-begin', value);
      this.updateModelValue();
    },
    onEndChange(value) {
      this.valueEnd = value;
      this.$emit('on-change-end', value);
      this.updateModelValue();
    },
    updateModelValue() {
      this.$emit('update:modelValue', {
        valueBegin: this.valueBegin,
        valueEnd: this.valueEnd,
      });
    },
    valueOfDate(date) {
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, 0);
      const day = date.getDate().toString().padStart(2, 0);

      // const h = date.getHours().toString().padStart(2, 0);
      // const m = date.getMinutes().toString().padStart(2, 0);
      // const s = date.getSeconds().toString().padStart(2, 0);

      switch (this.type) {
        case 'date':
        case 'datetime':
          return new Date(`${year}-${month}-${day}`).valueOf();
        case 'month':
          return new Date(`${year}-${month}-01`).valueOf();
        default:
          return date.valueOf();
      }
    },
  },
  emits: ['on-change-begin', 'on-change-end', 'update:modelValue'],
};
</script>
<style lang="less" scoped>
.date-range-wrap {
  display: flex;
  :deep(.ivu-input-with-suffix) {
    padding-right: 20px;
  }
}
</style>
