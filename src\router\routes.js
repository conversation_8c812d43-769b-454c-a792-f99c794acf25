import biyiAuthUtil from '../biyi-auth/init';

import Base from './modules/system/base';
import Home from './modules/home';
import Log from './modules/system/log';
import ErrorPage from './modules/system/error';
import Demo from './modules/demo'; // 示例

/**
 * 路由基本配置
 */
// [MEMO VU3升级] 暂时只搬运 登录 和 个人中心 页面
const { LoginRoute, personalRoute } = biyiAuthUtil.getInit();

const base = [...Base];

/**
 * 在主框架内显示
 */

const frameIn = [
  // 注意：正常页面的路由请不要以 /INGPAGE 结尾
  personalRoute,
  Home,
  Log,
  ...Demo,
  // ------------------ 以 /INGPAGE 结尾的路由，跳转到“开发中”页面 ------------------
  {
    path: '/:c(\\w*)*/INGPAGE',
    name: 'ingPage',
    meta: {
      skipAuth: true,
    },
    component: () => import('@/views/common/ing.vue'),
  },
];

/**
 * 在主框架之外显示
 */

const frameOut = [
  // 登录
  {
    ...LoginRoute,
    path: '/login',
    name: 'login',
    meta: {
      title: '$t:page.login.title', // TODO
      frameOut: true,
    },
  },
  // 放在最下面
  ...ErrorPage,
];
// 确保每个在frameIn下的路由有auth属性
frameIn.forEach((v) => {
  if (v.meta) {
    if (!v.meta.skipAuth) {
      !Object.prototype.hasOwnProperty.call(v.meta, 'auth') && (v.meta.auth = true);
    }
  } else {
    v.meta = {
      auth: true,
    };
  }
});

// 导出需要显示菜单的
export const frameInRoutes = frameIn;

// 重新组织后导出
export default [...base, ...frameIn, ...frameOut];
