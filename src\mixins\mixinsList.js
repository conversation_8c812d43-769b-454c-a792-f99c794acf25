/*
  使用示例
    mixins: [mixinsTable],
    data() {
      return {
        // 通用-搜索
        searchFormInitData: {},
        // 通用-列表
        listLoading: true,
        listConfig: {
          total: 0,
          size: 10,
          page: 1,
        },
        listData: [],
        // 组件配置-API
        componentConfigAPI: {
          getList: {
            apiFun: getFlowPackage
          },
        },
        // 组件配置-Button
        componentConfigButton: {
          addListItems: {
            routerName: 'flow-package-add',
          },
        },
        // 组件配置-列表、搜索
        searchFormListOrder: ['套餐名称'],
        listColumnsOrder: ['CHECKBOX', '套餐名称', 'ACTION'],
        componentItemsConfig: {
        },
      };
    },
    created() {
      this.initSearchFormData();
      this.refreshList();
    },

    searchFormItem 的示例见 createSearchForm.js
    listColumn 的示例见 createListColumns.js
*/
import { createSearchFormlist, getSearchFormData } from './createSearchForm.js';
import { createListColumns } from './createListColumns.js';
import { ceil } from '@/libs/lan';

export default {
  computed: {
    /**
     * 根据 componentItemsConfig 的配置，生成列表的 columns
     */
    listColumns: function () {
      const listColumns = createListColumns({
        listColumnsOrder: this.listColumnsOrder,
        pageItemsConfig: this.componentItemsConfig,
      });

      // 序号列
      const iINDEX = this.listColumnsOrder.indexOf('INDEX');
      if (iINDEX !== -1) {
        listColumns[iINDEX].indexMethod = (row, index) => {
          return row._index + 1 + this.listConfig.size * (this.listConfig.page - 1);
        };
      }

      return listColumns;
    },
    /**
     * 根据 componentItemsConfig 的配置，生成搜索的 columns
     */
    searchFormItems: function () {
      return createSearchFormlist({
        pageItemsConfig: this.componentItemsConfig,
      });
    },
  },
  methods: {
    /**
     * 刷新页面数据
     */
    async refreshList() {
      const { listData, total, responseData } = await this.getListData();
      await this.refreshListData({ listData, total });
    },
    async refreshListData({ listData, total = 0 }) {
      if (listData.length === 0 && total > 0) {
        // 当前页没有数据的话，翻到最后一页
        this.listConfig.page = ceil(total / this.listConfig.size);
        const res = await this.getListData();
        this.listData = res.listData;
        this.listConfig.total = res.total;
      } else {
        this.listData = listData;
        this.listConfig.total = total;
      }
    },

    /**
     * 设置搜索项的初始值
     */
    initSearchFormData() {
      this.searchFormInitData = {};
      this.searchFormListOrder.forEach((key) => {
        if (this.searchFormItems[key]) {
          const { prop, initValue, formItemType } = this.searchFormItems[key];
          if (initValue !== null && initValue !== undefined) {
            this.searchFormInitData[prop] = initValue;
          } else {
            if (formItemType === 'DateRangeSeparate') {
              this.searchFormInitData[prop] = {};
            }
          }
        }
      });
    },
    /**
     * 重置搜索
     */
    handleReset() {
      this.listConfig.page = 1;
      this.refreshList();
    },

    /**
     * 搜索
     */
    handleSearch() {
      this.refreshList();
    },

    /**
     * 获取搜索项的值
     */
    getSearchParams() {
      let searchFormData = this.searchFormInitData || {};
      if (this.$refs.searchForm) {
        searchFormData = this.$refs.searchForm.getSearchFormData();
      }
      return getSearchFormData({
        searchFormItems: this.searchFormItems,
        searchFormData,
      });
    },

    /**
     * 分页
     */
    changePage(page) {
      this.listConfig.page = page;
      this.refreshList();
    },
    changeSize(size) {
      this.listConfig.page = 1;
      this.listConfig.size = size;
      this.refreshList();
    },

    /**
     * 获取列表数据
     */
    getListData() {
      const {
        apiFun,
        successMsg,
        errMsg,
        convertRequestData = () => {}, // 根据页面业务更改 requestData
        replaceRequestData,
        convertResponseData = () => {}, // 根据页面业务更改 responseData
      } = this.componentConfigAPI.getList;
      return new Promise(async (resolve, reject) => {
        this.listLoading = true;

        let requestData = {
          ...this.getSearchParams(),
          pageIndex: this.listConfig.page - 1,
          size: this.listConfig.size,
        };
        convertRequestData({ requestData });
        if (replaceRequestData) {
          requestData = replaceRequestData({ requestData });
        }

        try {
          const response = await apiFun(requestData);

          const responseData = response || {};
          convertResponseData({ responseData });

          resolve({
            listData: responseData.records || [],
            total: responseData.total || 0,
            responseData,
          });
          this.listLoading = false;
        } catch (error) {
          resolve({
            listData: [],
            total: 0,
          });
          this.listLoading = false;
        }
      });
    },
  },
};
