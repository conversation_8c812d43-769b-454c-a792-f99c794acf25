<template>
  <div class="expand-wrap" @click="onClick">
    <span class="expand-label">{{ isExpandAll ? '全部收起' : '全部展开' }}</span>
    <Icon :type="isExpandAll ? 'ios-arrow-up' : 'ios-arrow-down'" />
  </div>
</template>
<script>
export default {
  props: {
    isExpandAll: {
      type: Boolean,
      default: false,
    },
  },
  methods: {
    onClick() {
      this.$emit('on-expand', !this.isExpandAll);
    },
  },
  emits: ['on-expand'],
};
</script>
<style lang="less" scoped>
.expand-wrap {
  color: rgb(45, 140, 240);
  cursor: pointer;
  .expand-label {
    margin-right: 4px;
  }
}
</style>
