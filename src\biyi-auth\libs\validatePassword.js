import { regPassword } from './reg';

export function validatePasswordFn(password) {
  const n = password.length;
  const x = [];
  const y = [];
  if (password.length < 10 || password.length > 20) {
    return false;
  }
  if (!regPassword.test(password)) {
    return false;
  }
  if (password.includes('ctsi') || password.includes('ctdi')) {
    return false;
  }
  for (let i = 0; i < n; i++) {
    const [letterX, letterY] = getXY(password[i]);
    x.push(letterX);
    y.push(letterY);
  }
  if (!judgePrintWard(x, y, n) && !judgePrintWard(x, y.reverse(), n)) {
    return true;
  }
  return false;
}

function judgePrintWard(arrX, arrY, n) {
  for (let i = 0; i <= n - 4; i++) {
    if (
      arrY[i + 3] - arrY[i + 2] === 1 &&
      arrY[i + 2] - arrY[i + 1] === 1 &&
      arrY[i + 1] - arrY[i] === 1 &&
      3 * arrX[i] === arrX[i + 3] + arrX[i + 2] + arrX[i + 1]
    ) {
      return true;
    }
  }
  for (let i = 0; i <= n - 4; i++) {
    if (
      arrX[i + 3] - arrX[i + 2] === 1 &&
      arrX[i + 2] - arrX[i + 1] === 1 &&
      arrX[i + 1] - arrX[i] === 1 &&
      3 * arrY[i] === arrY[i + 3] + arrY[i + 2] + arrY[i + 1]
    ) {
      return true;
    }
  }
  return false;
}

function getXY(letter) {
  const atlas = [
    ['`~·', '1！!', '2@@', '3#', '4$￥', '5%', '6^……', '7&', '8*', '9(（', '0）)', '-_', '=+'],
    [' ', 'qQ', 'wW', 'eE', 'rR', 'tT', 'yY', 'uU', 'iI', 'oO', 'pP', '[{【', ']}】', '\\|、'],
    [' ', 'aA', 'sS', 'dD', 'fF', 'gG', 'hH', 'jJ', 'kK', 'lL', ';:', '\'"’“'],
    [' ', 'zZ', 'xX', 'cC', 'vV', 'bB', 'nN', 'mM', ',《<', '.>》', '/?？'],
  ];
  for (let j = 0; j < 4; j++) {
    for (let k = 0; k < atlas[j].length; k++) {
      if (atlas[j][k].indexOf(letter) > -1) {
        return [j, k];
      }
    }
  }
}
