/**
 * 该文件启用 `@/store/index.js` 导入所有 vuex 模块。
 * 这个文件是一次性创建的，不应该被修改。
 */

// [MEMO VU3升级] 因为改用vite，文件导入的语法也需要修改

// const files = import.meta.glob('./modules/*.js', { eager: true }); // 当前 vite 版本是 2.9，不支持 eager: true
const files = import.meta.globEager('./modules/*.js');
const modules = {};
for (const path in files) {
  modules[path.replace(/(\.\/modules\/|\.js)/g, '')] = files[path].default;
}

export default {
  namespaced: true,
  modules,
};
