import _ from 'lodash';

/**
  ref="targetResizeObserver"

  data() {
    return {
      resizeObserver: null,
    };
  },
  methods: {
    resizePage() {
      const { targetHeight, targetWidth } = this.getPageSize();
    },
  },
 */
export default {
  watch: {
    menuCollapse: {
      deep: true,
      handler: function (val) {
        this.debounceResizePage();
      },
    },
  },
  mounted() {
    const targetDOM = this.$refs.targetResizeObserver; // .$el 返回是 VueComponent 对象
    if (targetDOM) {
      this.resizeObserver = new ResizeObserver(this.debounceResizePage);
      this.resizeObserver.observe(targetDOM);

      this.debounceResizePage();
    }
  },
  beforeUnmount() {
    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
    }
  },
  methods: {
    debounceResizePage: _.debounce(function () {
      this.resizePage();
    }, 300),
    resizePage() {
      const { targetHeight, targetWidth } = this.getPageSize();
      // 在这里写各页面的处理
    },
    getPageSize() {
      const targetDOM = this.$refs.targetResizeObserver;
      let targetHeight = 0;
      let targetWidth = 0;
      if (targetDOM) {
        const targetStyle = window.getComputedStyle(targetDOM);
        targetHeight = this.getPX(targetStyle.height);
        targetWidth = this.getPX(targetStyle.width);
      }

      return { targetHeight, targetWidth };
    },
    getPX(str = '0px') {
      let num = str.replace('px', '');
      return parseInt(num, 10);
    },
  },
};
