import { qiankunWindow } from 'vite-plugin-qiankun/dist/helper';
export default {
  mounted() {
    if (qiankunWindow.__POWERED_BY_QIANKUN__) {
      // 开启乾坤（微前端）
      this.onMapReady();
    } else {
      this.loadMapScript().then(() => {
        this.onMapReady();
      });
    }
  },
  methods: {
    loadMapScript() {
      return new Promise((resolve, reject) => {
        window.onCallBackBaiduMap = () => {
          resolve();
        };
        const ak = 'R4yxAmBM1x35TDlT5mseVjMvugL8fVzl';
        const script = document.createElement('script');
        script.src = `https://api.map.baidu.com/api?v=1.0&type=webgl&ak=${ak}&callback=onCallBackBaiduMap`;
        document.body.appendChild(script);
      });
    },
    onMapReady() { },
  },
};
