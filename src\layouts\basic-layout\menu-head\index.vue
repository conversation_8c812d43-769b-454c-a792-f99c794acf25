<template>
  <div class="i-layout-menu-head" :class="{ 'i-layout-menu-head-mobile': isMobile }">
    <template v-if="!isMobile && filterHeaderUnfold.length">
      <Menu ref="menu" mode="horizontal" :active-name="headerName">
        <MenuItem
          v-for="item in filterHeaderUnfold"
          :key="item.path"
          :to="item.path"
          :replace="item.replace"
          :target="item.target"
          :name="item.name"
          @click="handleClick(item.path, 'header')"
        >
          <i-menu-head-title :item="item" />
        </MenuItem>
      </Menu>
      <Dropdown v-if="filterHeaderFold.length" class="filter-header-fold" trigger="click" :class="{ 'i-layout-menu-head-mobile-drop': isMobile }">
        <Icon type="ios-apps" size="18" />
        <template v-slot:list>
          <DropdownMenu>
            <i-link
              v-for="item in filterHeaderFold"
              :key="item.path"
              :to="item.path"
              :replace="item.replace"
              :target="item.target"
              @click="handleClick(item.path, 'header')"
            >
              <DropdownItem>
                <i-menu-head-title :item="item" />
              </DropdownItem>
            </i-link>
          </DropdownMenu>
        </template>
      </Dropdown>
    </template>

    <div v-else class="i-layout-header-trigger i-layout-header-trigger-min i-layout-header-trigger-in i-layout-header-trigger-no-height">
      <Dropdown trigger="click" :class="{ 'i-layout-menu-head-mobile-drop': isMobile }">
        <Icon type="ios-apps" />
        <template v-slot:list>
          <DropdownMenu>
            <i-link
              v-for="item in filterHeader"
              :key="item.path"
              :to="item.path"
              :replace="item.replace"
              :target="item.target"
              @click="handleClick(item.path, 'header')"
            >
              <DropdownItem>
                <i-menu-head-title :item="item" />
              </DropdownItem>
            </i-link>
          </DropdownMenu>
        </template>
      </Dropdown>
    </div>
    <Menu v-if="!isMobile" id="biyi-workbench-sub-header-menu" ref="subMenu" mode="horizontal" :active-name="headerName">
      <MenuItem
        v-for="item in filterHeader"
        :key="item.path"
        :to="item.path"
        :replace="item.replace"
        :target="item.target"
        :name="item.name"
        @click="handleClick(item.path, 'header')"
      >
        <i-menu-head-title :item="item" />
      </MenuItem>
    </Menu>
  </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex';
import { getStyle } from 'view-ui-plus/src/utils/assist';
import { off, on } from 'view-ui-plus/src/utils/dom';
import { throttle } from 'lodash';
import clickItem from '../mixins/click-item';
import iMenuHeadTitle from './title';

export default {
  name: 'IMenuHead',
  components: { iMenuHeadTitle },
  mixins: [clickItem],
  data() {
    return {
      handleResize: () => {},
      isMenuLimit: false,
      menuMaxWidth: 0, // 达到这个值后，menu 就显示不下了

      filterHeaderFold: [],
      filterHeaderUnfold: [],
    };
  },
  computed: {
    ...mapState('admin/layout', ['isMobile']),
    ...mapState('admin/menu', ['headerName']),
    ...mapGetters('admin/menu', ['filterHeader']),
  },
  watch: {
    filterHeader() {
      this.handleGetMenuHeight();
    },
    isMobile() {
      this.handleGetMenuHeight();
    },
  },
  mounted() {
    this.handleResize = throttle(this.handleGetMenuHeight, 100, {
      leading: false,
    });
    on(window, 'resize', this.handleResize);
    this.handleGetMenuHeight();
  },
  beforeUnmount() {
    off(window, 'resize', this.handleResize);
  },
  methods: {
    handleGetMenuHeight() {
      setTimeout(() => {
        const menuWidth = parseInt(getStyle(this.$el, 'width'));
        const subMenuWidth = this.$refs.subMenu ? parseInt(getStyle(this.$refs.subMenu.$el, 'width')) : 0;
        // 顶部菜单实际宽度超过父容器的宽度
        if (subMenuWidth > menuWidth) {
          const children = Array.from(this.$refs.subMenu.$el.children);
          let [i, widthSum] = [0, 42];
          while (i < children.length) {
            widthSum += parseInt(getStyle(children[i], 'width'));
            if (widthSum > menuWidth) {
              break;
            }
            i++;
          }
          this.filterHeaderUnfold = this.filterHeader.slice(0, i);
          this.filterHeaderFold = this.filterHeader.slice(i);
        } else {
          this.filterHeaderUnfold = this.filterHeader.slice(0);
          this.filterHeaderFold = [];
        }
      }, 100);
    },
  },
};
</script>

<style lang="less">
.filter-header-fold {
  float: right;
}
.filter-header-fold:hover {
  background-color: rgba(255, 255, 255, 0.05);
  cursor: pointer;
}
#biyi-workbench-sub-header-menu {
  position: fixed;
  top: -300px;
  left: 0;
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
}
</style>
