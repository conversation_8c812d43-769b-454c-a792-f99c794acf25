import * as commonAPI from '../common';

export function getCarCompanyList(args = {}) {
  return new Promise(async (resolve, reject) => {
    try {
      const { requestData = {} } = args;
      const response = await commonAPI.getCarCompanyList(requestData);
      const listData = response || [];
      const carCompanyList = listData.map((item) => {
        return {
          label: item.customName,
          value: item.customId,
        };
      });

      resolve({
        carCompanyList,
      });
    } catch (error) {
      resolve({
        carCompanyList: [],
      });
    }
  });
}
