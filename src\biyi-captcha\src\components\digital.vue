<template>
  <div>
    <img :src="imgUrl" :width="width" :height="height" alt="验证码图片加载错误" title="点击刷新验证码" @click="initImageCatpcha" />
  </div>
</template>

<script>
import service from '../../request/index.js';
export default {
  name: 'DigitalCaptcha',

  props: {
    width: {
      type: [String, Number],
      default: '150',
    },
    height: {
      type: [String, Number],
      default: '40',
    },
    baseUrl: {
      type: String,
      required: true,
    },
  },

  data() {
    return {
      imgUrl: '',
    };
  },

  mounted() {
    this.initImageCatpcha();
  },

  methods: {
    /**
     * @description: 初始化验证码
     */
    initImageCatpcha() {
      this.getSecurityCode();
    },
    /**
     * @description: 获取数字验证码，并向父组件传递验证数据
     */
    getSecurityCode() {
      service
        .get(`/api/captcha/digital-captcha`)
        .then((response) => {
          const data = {
            key: Object.keys(response.data)[0],
          };
          if (response) {
            let key = Object.keys(response.data)[0];
            this.imgUrl = response.data[key];
            this.$emit('getVerifyRes', data);
          }
        })
        .catch();
    },
  },
  emits: ['getVerifyRes'],
};
</script>
