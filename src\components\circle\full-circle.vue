<template>
  <div class="circle-main">
    <div class="circle-main-box" :style="[{ width: size + 'px', height: size + 'px' }]">
      <svg :width="size" :height="size" class="circle">
        <circle :r="radius" :cx="cx" :cy="cy" fill="transparent" stroke="#EEEEEE" :stroke-width="strokeWidth" />
        <circle
          :r="radius"
          :cx="cx"
          :cy="cy"
          fill="transparent"
          :stroke="color"
          :stroke-width="strokeWidth"
          stroke-linecap="round"
          :stroke-dasharray="circumference"
          :stroke-dashoffset="progress"
        />
      </svg>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    // 进度值
    value: {
      type: [String, Number],
      default: 0,
    },
    // 尺寸
    size: {
      type: [String, Number],
      default: 120,
    },
    // 边框粗细
    strokeWidth: {
      type: [String, Number],
      default: 10,
    },
    // 进度条颜色
    color: {
      type: String,
      default: 'rgba(153,202,251,1)',
    },
  },
  data() {
    return {
      now: 0,
    };
  },
  computed: {
    percentage() {
      return this.value;
    },
    // 圆心x轴坐标
    cx() {
      return this.size / 2;
    },
    // 圆心y轴坐标
    cy() {
      return this.size / 2;
    },
    // 半径
    radius() {
      return (this.size - this.strokeWidth) / 2;
    },
    // 圆周长
    circumference() {
      return 2 * Math.PI * this.radius;
    },
    // 进度长度
    progress() {
      return (1 - this.percentage / 100) * this.circumference;
    },
  },
};
</script>
<style lang="less" scoped>
.circle {
  transform: rotate(-90deg);
}
.circle-main-box {
  position: relative;
  display: block;
  margin: 0 auto;
}
</style>
