import _ from 'lodash';
import { mapState } from 'vuex';

export default {
  computed: {
    ...mapState('admin/layout', ['menuCollapse']),
  },
  watch: {
    menuCollapse: {
      deep: true,
      handler: function (val) {
        this.debounceResizePage();
      },
    },
  },
  created() {
    window.addEventListener('resize', this.debounceResizePage);
  },
  mounted() {
    this.debounceResizePage();
  },
  beforeUnmount() {
    window.removeEventListener('resize', this.debounceResizePage);
  },
  methods: {
    debounceResizePage: _.debounce(function () {
      this.resizePage();
    }, 300),
    resizePage() {
      const { pageHeight, pageWidth } = this.getPageSize();
      // 在这里写各页面的处理
    },
    getPageSize() {
      let pageWidth = 0;
      let pageHeight = 0;
      let foldMemu = false;

      const mainContent = document.getElementsByClassName('i-layout-content-main');
      if (mainContent && mainContent[0]) {
        const mainContentStyle = window.getComputedStyle(mainContent[0]);
        // pageWidth = this.getPX(mainContentStyle.width) - this.getPX(mainContentStyle.paddingLeft) + this.getPX(mainContentStyle.paddingRight);
        pageWidth = this.getPX(mainContentStyle.width);
        // pageHeight = this.getPX(mainContentStyle.height) - this.getPX(mainContentStyle.paddingTop) + this.getPX(mainContentStyle.paddingBottom);
        pageHeight = this.getPX(mainContentStyle.height);
      }

      // 暂时没有做左侧菜单展开、收缩的监听，为避免出现滚动条，多扣除些宽度
      const headerTrigger = document.getElementsByClassName('i-layout-header-trigger');
      if (headerTrigger && headerTrigger[0]) {
        const headerTriggerUnfold = headerTrigger[0].getElementsByClassName('i-icon-menu-unfold');
        if (headerTriggerUnfold && headerTriggerUnfold[0]) {
          const headerTriggerUnfoldStyle = window.getComputedStyle(headerTriggerUnfold[0]);
          if (headerTriggerUnfoldStyle.display !== 'none') {
            pageWidth -= 120;
          } else {
            foldMemu = true;
          }
        }
      }

      return {
        pageWidth,
        pageHeight,
        foldMemu,
      };
    },
    getPX(str = '0px') {
      let num = str.replace('px', '');
      return parseInt(num, 10);
    },
  },
};
