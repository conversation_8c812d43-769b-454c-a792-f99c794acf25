import service from '../http/http';

export function sendEmail(param, { key, value }) {
  return service({
    url: '/api/email-codes',
    method: 'get',
    params: param,
    timeout: 50000,
    headers: {
      biyiCaptchaKey: key,
      biyiCaptcha: value,
    },
  });
}

export function validateEmailAndCode(data) {
  return service({
    url: '/api/email-codes',
    method: 'post',
    data,
  });
}
