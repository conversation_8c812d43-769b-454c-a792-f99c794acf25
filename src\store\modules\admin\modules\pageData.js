/**
 * 页面数据
 * */

export default {
  namespaced: true,
  state: {
    pageDataDict: {},
    parentRouterDict: {},
  },
  mutations: {
    setPageData(state, { routerName, pageData }) {
      state.pageDataDict[routerName] = pageData;
    },
    clearPageData(state, { routerName }) {
      state.pageDataDict[routerName] = {};
    },
    checkParentPageData(state, { fromRouterName, toRouterName }) {
      const parentAry = state.parentRouterDict[fromRouterName] || [];
      if (!parentAry.includes(toRouterName)) {
        // 不是跳转回父页面的话，就清空保留的父页面的数据
        parentAry.forEach((routerName) => {
          state.pageDataDict[routerName] = {};
        });
        state.pageDataDict[toRouterName] = {};
      }
    },
    setParentRouter(state, { fromRouterName, toRouterName }) {
      const parentAry = state.parentRouterDict[fromRouterName] || [];
      state.parentRouterDict[toRouterName] = [...parentAry, fromRouterName];
    },
  },
  actions: {
    /**
     * @description 路由跳转的时候，保存页面数据以便返回页面后恢复
     */
    savePageData({ commit, dispatch }, { to, from }) {
      if (from && from.name && to && to.name) {
        const fromRouterName = from.name;
        const toRouterName = to.name;
        let toChildrenPage = false;
        if (to.params) {
          if (to.params.toChildrenPage === 'true' || to.params.toChildrenPage === true) {
            toChildrenPage = true;
            commit('setParentRouter', { fromRouterName, toRouterName });
          }
          if (to.params.savePageData) {
            commit('setPageData', {
              routerName: fromRouterName,
              pageData: JSON.parse(to.params.savePageData),
            });
          }
        }

        if (!toChildrenPage) {
          commit('checkParentPageData', { fromRouterName, toRouterName });
        }
      } else if (to && to.name) {
        commit('clearPageData', { routerName: to.name });
      }
    },
  },
};
