<template>
  <Submenu :name="menu.path">
    <template v-slot:title>
      <i-menu-side-title :menu="menu" />
      <Badge v-bind="badgeData" v-if="badgeData" class="i-layout-menu-side-badge" />
    </template>
    <template v-for="(item, index) in menu.children" :key="index">
      <i-menu-side-item v-if="item.children === undefined || !item.children.length" :menu="item" />
      <i-menu-side-submenu v-else :menu="item" />
    </template>
  </Submenu>
</template>

<script>
import menuBadge from '../mixins/menu-badge';
import iMenuSideItem from './menu-item';
import iMenuSideTitle from './menu-title';

export default {
  name: 'IMenuSideSubmenu',
  components: { iMenuSideItem, iMenuSideTitle },
  mixins: [menuBadge],
  props: {
    menu: {
      type: Object,
      default() {
        return {};
      },
    },
  },
};
</script>
