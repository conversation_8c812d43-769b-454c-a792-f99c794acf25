import axios from 'axios';

import { Message, Notice } from 'view-ui-plus';
import Util from '../libs/util';
import $biyiSetting from '@/config/setting.auth'; // [MEMO VU3升级] 原版比翼框架安装的时候设置到 Vue.prototype.$biyiSetting 中的

const service = axios.create({
  baseURL: window.$biyiAuthAPIUrl, // [MEMO VU3升级] 原版比翼框架是通过 setAxiosConfigs 在安装的时候设置 baseURL
  withCredentials: true,
  timeout: 50000, // 请求超时时间
});

// 创建一个错误
function errorCreate(msg) {
  const err = new Error(msg);
  errorLog(err);
  throw err;
}

// 记录和显示错误
function errorLog(err) {
  // 打印到控制台
  if (process.env.NODE_ENV === 'development') {
    // util.log.error('>>>>>> Error >>>>>>');
    console.log(err);
  }
  // 显示提示，可配置使用 iView 的 $Message 还是 $Notice 组件来显示
  if ($biyiSetting.errorModalType === 'Message') {
    Message.error({
      content: err.message,
      duration: 3,
    });
  } else if ($biyiSetting.errorModalType === 'Notice') {
    Notice.error({
      title: '提示',
      desc: err.message,
      duration: 3,
    });
  }
}

const requestInterceptor = {
  config: (config) => {
    if (!config.url.includes('/api/system/login')) {
      const token = Util.cookies.get('token');
      const tokenHeader = $biyiSetting.tokenName || 'Authorization';
      // config.withCredentials = true
      if (!config.headers[tokenHeader]) config.headers[tokenHeader] = token;
    }
    return config;
  },
  error: (error) => {
    console.log(error);
    Promise.reject(error);
  },
};

const responseInterceptor = {
  response: (response) => {
    // dataAxios 是 axios 返回数据中的 data
    const dataAxios = response.data;
    // 根据状态判断
    const code = response.status;
    // 根据 code 进行判断
    if (code === undefined) {
      // 如果没有 code 代表这不是项目后端开发的接口
      return dataAxios;
    } else {
      // 有 code 代表这是一个后端接口 可以进行进一步的判断
      switch (code) {
        case 200:
          // [ 示例 ] code === 0 代表没有错误
          return dataAxios;
        case '401':
          // [ 示例 ] 其它和后台约定的 code
          errorCreate(`[ code: 401 ] ${dataAxios.msg}: ${response.config.url}`);
          break;
        default:
          // 不是正确的 code
          errorCreate(`${dataAxios.msg}: ${response.config.url}`);
          break;
      }
    }
  },
  error: (error) => {
    if (error && error.response) {
      switch (error.response.status) {
        case 400:
          error.message = '请求错误';
          break;
        case 401:
          error.message = '未授权，请登录';
          if (error.response.config.url === '/api/system/login') {
            error.message = '用户名或密码错误，请重新登录';
          } else if (window.$vuePrototype && window.$vuePrototype.$errorInterceptor) {
            window.$vuePrototype.$errorInterceptor(401);
          }
          // 重新登录cas
          // todo==========================================================
          // store.dispatch('admin/account/login');
          break;
        case 403:
          error.message = '拒绝访问';
          break;
        case 404:
          error.message = `请求地址出错: ${error.response.config.url}`;
          break;
        case 408:
          error.message = '请求超时';
          break;
        default:
          error.message = (error.response.data && error.response.data.message) || '接口异常';
          // Message.error({
          //     content: error.response.data && error.response.data.detail || '接口异常',
          //     duration: 2
          // })
          break;
      }
    }
    errorLog(error);
    return Promise.reject(error);
  },
};

export function setRequestInterceptor(config, error) {
  if (Util.judgeFunction(config)) {
    requestInterceptor.config = config;
  }
  if (Util.judgeFunction(error)) {
    requestInterceptor.error = error;
  }
  service.interceptors.request.use(requestInterceptor.config, requestInterceptor.error);
}
export function setResponseInterceptor(response, error) {
  if (Util.judgeFunction(response)) {
    responseInterceptor.response = response;
  }
  if (Util.judgeFunction(error)) {
    responseInterceptor.error = error;
  }
  service.interceptors.response.use(responseInterceptor.response, responseInterceptor.error);
}
service.interceptors.request.use(requestInterceptor.config, requestInterceptor.error);
service.interceptors.response.use(responseInterceptor.response, responseInterceptor.error);

export default (opts) => service(opts);
