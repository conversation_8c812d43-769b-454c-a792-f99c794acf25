import store from '@/store/index';

const layout = {};
/**
 * @description: 手动隐藏、显示侧边栏
 * @param {Boolean} hide 是否隐藏
 */
layout.hideSider = function (hide = false) {
  store.commit('admin/menu/setManualHideSider', hide);
};

/**
 * @description: 手动设置顶部菜单名
 * @param {Boolean} header 顶栏菜单名
 */
layout.setHeader = function (header = '') {
  store.commit('admin/menu/setHeaderName', header);
};

/**
 * @description: 手动隐藏、显示面包屑
 * @param {Boolean} hide 是否隐藏
 */
layout.hideBreadcrumb = function (hide = false) {
  store.commit('admin/menu/setManualHideBreadcrumb', hide);
};

export default layout;
