/**
 * 多语言
 * */
import Languages from '@/i18n/locale';
import Setting from '@/config/setting';

export default {
  namespaced: true,
  state: {
    locale: '',
  },
  actions: {
    /**
     * @description 获取当前语言
     * */
    getLocale({ state }) {
      let locale;

      if (Setting.i18n.auto) {
        // 如果自动识别的语言，本地没有该语言包，则设置为默认语言
        const navLang = navigator.language;
        if (Languages[navLang]) {
          locale = navLang;
        } else {
          locale = Setting.i18n.default;
        }
      } else {
        locale = Setting.i18n.default;
      }
      state.locale = locale;
    },
    /**
     * @description 设置当前语言
     * */
    setLocale({ state }, { locale = Setting.i18n.default, vm }) {
      // 设置当前语言
      state.locale = locale;
      // 设置 vue-i18n 的语言
      vm.$i18n.locale = locale;
      // 更新网页标题
      // util.title({
      //     title: vm.$route.meta.title
      // });
    },
  },
};
