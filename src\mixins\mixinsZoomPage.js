export default {
  created() {
    window.addEventListener('resize', this.resizePage);
  },
  mounted() {
    this.resizePage();
  },
  beforeUnmount() {
    window.removeEventListener('resize', this.resizePage);
  },
  methods: {
    resizePage() {
      const browerWidth = window.innerWidth;
      const browerHeight = window.innerHeight;
      const zoom = Math.min(browerWidth / 1920, browerHeight / 1080);
      const zoomContent = document.getElementsByClassName('dashboard-page');
      if (zoomContent && zoomContent[0]) {
        zoomContent[0].style.transform = `scale(${zoom})`;
      }
    },
  },
};
